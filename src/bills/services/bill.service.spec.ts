/* eslint-disable max-len */
/* eslint max-lines: off */
import { BadRequestException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import Chance from 'chance';
import { EntityManager } from 'typeorm';
import { BillService } from './bill.service';
import { MockedQueryBuilder } from '../../__mocks__/database.mock';
import { TestDataSourceOptions } from '../../data-source';
import { VirtualServicesPaymentInput } from '../inputs/virtual-services-payment.input';
import { BillDetailsModel } from '../models/bill-details.model';
import { BillModel } from '../models/bill.model';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { walletTransactionFactory } from '@clinify/__mocks__/factories/wallet-transaction.factory';
import { loggerMock } from '@clinify/__mocks__/logger';
import { AdmissionModel } from '@clinify/admissions/models/admission.model';
import {
  VirtualAccountProvider,
  VirtualAccountTransactionType,
  VirtualAccountType,
} from '@clinify/banks/enum/virtual-account.enum';
import { BankService } from '@clinify/banks/services/bank.service';
import { VirtualServicesPaymentModel } from '@clinify/bills/models/virtual-services-payment.model';
import { CommissionPayer } from '@clinify/facility-preferences/enums/commission-payer';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { PaymentDepositService } from '@clinify/payment-deposits/services/payment-deposit.service';
import { AccountHolder } from '@clinify/shared/enums/account-holder';
import {
  BillStatus,
  FundTransferStatus,
} from '@clinify/shared/enums/appointment';
import {
  DeliveryStatus,
  ReceiptStatus,
  ServiceType,
} from '@clinify/shared/enums/bill';
import { Currency } from '@clinify/shared/enums/currency';
import { UserType } from '@clinify/shared/enums/users';
import { MAILER } from '@clinify/shared/mailer/constants';
import { TransactionProfileService } from '@clinify/shared/services/transacting-profile.service';
import { ValidatePasscodeService } from '@clinify/shared/services/validate-passcode.service';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { UserModel } from '@clinify/users/models/user.model';
import { WalletService } from '@clinify/wallets/services/wallet.service';
import { billDetailsFactory, billFactory } from '@mocks/factories/bill.factory';
import { dispenseDetailsfactory } from '@mocks/factories/medication.factory';
import { mockPaymentDeposit } from '@mocks/factories/payment-deposit.factory';
import { userFactory } from '@mocks/factories/user.factory';

const chance = new Chance();
const user: UserModel = userFactory.build();
const hospital: HospitalModel = hospitalFactory.build();
const walletTransaction = walletTransactionFactory.build();

const profile = new ProfileModel();
profile.type = 'Patient';
profile.isDefault = true;
profile.id = chance.guid({ version: 4 });

const managerMock: any = {
  createOrgBill: jest.fn(() => bill),
  billRepo: {
    createOrgBill: jest.fn(() => bill),
    findOne: jest.fn(() => bill),
  },
  createQueryBuilder: MockedQueryBuilder,
  queryRunner: { isTransactionActive: true },
  save: jest.fn((v) => v),
  delete: jest.fn(),
  withRepository: jest.fn((v) => ({
    ...v,
    createQueryBuilder: MockedQueryBuilder,
    findOne: jest.fn(() => bill),
  })),
  findOne: jest.fn(),
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const mockBankService = {
  generateVirtualAccountNumber: jest.fn(() =>
    Promise.resolve({
      id: 'virtual-bank-account-id',
      accountName: 'Test Account',
      accountNumber: '**********',
      bank: VirtualAccountProvider.WEMA,
      currency: Currency.KOBO,
      virtualAccountType: VirtualAccountType.Temporary,
      transactionType: VirtualAccountTransactionType.PayBill,
      isActive: true,
      createdDate: new Date(),
      updatedDate: new Date(),
    }),
  ),
};

const MockPaymentDepositService = {
  addPaymentDeposit: jest.fn(() => mockPaymentDeposit),
  makeServicePaymentFromDeposit: jest.fn(() => mockPaymentDeposit),
  getPaymentDepositSummary: jest.fn(() => Promise.resolve({ balance: 0 })),
};

const mockMailer = {
  sendMail: jest.fn(),
};

const mockProfileRepository = {
  byClinifyId: jest.fn(() => user.defaultProfile),
  findOne: jest.fn(() => user.defaultProfile),
};

const bills = billFactory.buildList(2);
const bill = bills[0];
const billDetails = billDetailsFactory.build();

const mockWalletService = {
  transferFunds: jest.fn(() => walletTransaction),
  chargePatientWallet: jest.fn(() => walletTransaction),
};

const mockBillRepository = {
  createQueryBuilder: jest.fn(() => ({
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    getOne: jest.fn(() => bill),
  })),
  getBill: jest.fn(() => bill),
  findOne: jest.fn(() => bill),
  findOneOrFail: jest.fn(() =>
    Promise.resolve({
      ...bill,
      receiverProfile: {
        ...bill?.receiverProfile,
        user: { email: chance.email() },
      },
    }),
  ),
  bilRefExists: jest.fn(() => true),
  createBill: jest.fn(() => bill),
  getCreatedBillsByClinifyId: jest.fn(() => bills),
  getBillsByClinifyId: jest.fn(() => bills),
  getUnpaidBillsByClinifyId: jest.fn(() => bills),
  getUnfulfilledPaidBills: jest.fn(() => bills),
  getHospitalBills: jest.fn(() => bills),
  getPatientBills: jest.fn(() => bills),
  updateBill: jest.fn(() => bill),
  deleteBill: jest.fn(() => bill),
  getBillByReference: jest.fn(() => bill),
  byCllinifyId: jest.fn(() => hospital),
  createOrgBill: jest.fn(() => bill),
  updateOrgBill: jest.fn(() => bill),
  deleteOrgBills: jest.fn(() => bill),
  archiveBills: jest.fn(() => bill),
  archiveBillsR: jest.fn(() => bill),
  cancelOrgBill: jest.fn(() => bill),
  unCancelOrgBill: jest.fn(() => bill),
  recallOrgBill: jest.fn(() => bill),
  addBillDetail: jest.fn(() => billDetails),
  updateBillDetail: jest.fn(() => billDetails),
  deleteBillDetail: jest.fn(() => billDetails),
  findBillByProfile: jest.fn(() => ({ list: [bill], totalCount: 1 })),
  findBillByHospital: jest.fn(() => ({ list: [bill], totalCount: 1 })),
  excludeBillDetail: jest.fn(() => bill),
  save: jest.fn((v) => v),
  addOrgBill: jest.fn(() => billDetails),
  delete: jest.fn(),
  createWalkInBill: jest.fn(),
};

const mockValidatePasscodeService = {
  validatePasscode: jest.fn(),
};

const mockTransactionProfileService = {
  getTransactingProfile: jest.fn(() => ({
    profileType: 'profile',
    profile: { id: 'testId', clinifyId: '1234qwertt' },
  })),
  getTransactingProfileForFetching: jest.fn(() => ({
    profileType: 'profile',
    profile: { id: 'testId', clinifyId: '1234qwertt' },
  })),
};

const mockHospitalRepository = {
  byClinifyId: jest.fn(() => hospital),
  findOne: jest.fn(() => hospital),
};

const mockBillDetailsRepository = {
  updateParentBill: jest.fn(),
};

describe('BillService', () => {
  let service: BillService;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [
        BillService,
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: mockProfileRepository,
        },
        {
          provide: WalletService,
          useValue: mockWalletService,
        },
        {
          provide: TransactionProfileService,
          useValue: mockTransactionProfileService,
        },
        {
          provide: getRepositoryToken(HospitalModel),
          useValue: mockHospitalRepository,
        },
        {
          provide: MAILER,
          useValue: mockMailer,
        },
        {
          provide: getRepositoryToken(BillModel),
          useValue: mockBillRepository,
        },
        {
          provide: EntityManager,
          useValue: managerMock,
        },
        {
          provide: ValidatePasscodeService,
          useValue: mockValidatePasscodeService,
        },
        {
          provide: getRepositoryToken(BillDetailsModel),
          useValue: mockBillDetailsRepository,
        },
        {
          provide: PaymentDepositService,
          useValue: MockPaymentDepositService,
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
        {
          provide: BankService,
          useValue: mockBankService,
        },
        { ...loggerMock },
      ],
    }).compile();
    service = module.get(BillService);
    jest // @ts-ignore
      .spyOn(service.dataSource, 'transaction')
      .mockImplementation((cb) => {
        // @ts-ignore
        return cb(managerMock);
      });
  });

  it('getBill() should get a bill record', async () => {
    const billRecord = await service.getBill(user.defaultProfile, bill.id);
    expect(billRecord).toEqual(bill);
  });

  it('getBillByReference() should returnn a billl', async () => {
    const billRecord = await service.getBillByReference(
      user.defaultProfile,
      bill.reference,
    );
    expect(billRecord).toEqual(bill);
  });

  it('cancelBillOrChargeLevy() should return error if payment status is completed', async () => {
    const receiverProfile = user.defaultProfile;
    const newBill = {
      ...billFactory.build(),
      receiverProfile,
      senderProfile: profileFactory.build(),
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountPaid: 0,
      deliveryConfirmation: DeliveryStatus.Delivered,
      receiptConfirmation: ReceiptStatus.Received,
      paymentStatus: FundTransferStatus.Completed,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill);
    try {
      await service.cancelBillOrChargeLevy(
        {
          profile: receiverProfile,
          billId: bill.id,
        },
        true,
      );
    } catch ({ message }) {
      expect(message).toEqual(
        'Bill Cannot Be Cancelled Until The Service Is Proven Unfulfilled',
      );
    }
  });

  it('cancelBillOrChargeLevy() should return error if payment status is completed', async () => {
    const receiverProfile = user.defaultProfile;
    const newBill = {
      ...billFactory.build(),
      receiverProfile,
      senderProfile: profileFactory.build(),
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountPaid: 0,
      deliveryConfirmation: DeliveryStatus.Pending,
      receiptConfirmation: ReceiptStatus.Pending,
      billStatus: BillStatus.CancelledWithLevy,
      paymentStatus: FundTransferStatus.Completed,
    };
    mockBillRepository.getBillByReference = jest
      .fn()
      .mockImplementationOnce(() => newBill);
    try {
      await service.cancelBillOrChargeLevy({
        profile: receiverProfile,
        billRef: bill.id,
        passCode: 'correctPasscode',
      });
    } catch ({ message }) {
      expect(message).toEqual('Bill Already Cancelled');
    }
  });

  it('cancelBillOrChargeLevy() should cancle a bill successfully by user', async () => {
    const receiverProfile = { ...user.defaultProfile, id: 'testId' };
    const newBill = {
      ...billFactory.build(),
      receiverProfile,
      senderProfile: profileFactory.build(),
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Completed,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill);

    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.cancelBillOrChargeLevy({
      profile: newBill.receiverProfile,
      billId: bill.id,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  it('cancelBillOrChargeLevy() should throw error if proccessing fails', async () => {
    const receiverProfile = { ...user.defaultProfile, id: 'testId' };
    const newBill = {
      ...billFactory.build(),
      receiverProfile,
      senderProfile: profileFactory.build(),
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Completed,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill);

    mockBillRepository.updateBill = jest.fn().mockImplementationOnce(() => {
      throw Error();
    });
    try {
      await service.cancelBillOrChargeLevy({
        profile: newBill.receiverProfile,
        billId: bill.id,
        passCode: 'correctPasscode',
      });
    } catch ({ message }) {
      expect(message).toEqual('Error Cancelling Bill');
    }
  });

  it('cancelBillOrChargeLevy() should perform only a cancle operation when sender initiates cancellation', async () => {
    const senderProfile = { ...user.defaultProfile, id: 'testId' };
    const newBill = {
      ...billFactory.build(),
      receiverProfile: profileFactory.build(),
      senderProfile,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Completed,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.cancelBillOrChargeLevy({
      profile: newBill.senderProfile,
      billId: bill.id,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  it('payBill() should pay a bill successfully when payment status is pending', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Pending,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.payBill(user.defaultProfile, {
      id: bill.id,
      amount: 950,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  it('payBill() should not check password is the payment is auto triggered', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      totalAmount: 1000,
      discountAmount: null,
      amountPayable: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Pending,
    };
    const autoTriggered = true;
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.payBill(
      user.defaultProfile,
      {
        id: bill.id,
        amount: 950,
        passCode: null,
      },
      autoTriggered,
    );
    expect(billPayed).toEqual(bill);
  });

  it('payBill() should pay bill balance successfully when payment status is PartialPayment', async () => {
    const newBill = {
      ...billFactory.build(),
      senderProfile: {
        ...profileFactory.build(),
        type: UserType.OrganizationDoctor,
        fullName: 'qwert',
        hospital: { name: 'qwerty' },
      },
      receiverprofile: profileFactory.build(),
      totalAmount: 1000,
      discountAmount: 200,
      amountPayable: 800,
      amountPaid: 500,
      paymentStatus: FundTransferStatus.PartialPayment,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.payBill(user.defaultProfile, {
      id: bill.id,
      amount: 300,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  it('payBill() should throw Error when amount passed is less than balance for PartialPayment payment status', async () => {
    const newBill = {
      ...billFactory.build(),
      senderProfile: {
        ...profileFactory.build(),
        type: UserType.OrganizationDoctor,
        fullName: 'qwert',
        hospital: { name: 'qwerty' },
      },
      receiverprofile: profileFactory.build(),
      totalAmount: 1000,
      discountAmount: 200,
      amountPayable: 800,
      amountPaid: 500,
      paymentStatus: FundTransferStatus.PartialPayment,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    try {
      await service.payBill(user.defaultProfile, {
        id: bill.id,
        amount: 200,
        passCode: 'correctPasscode',
      });
    } catch ({ message }) {
      expect(message).toEqual('Payment Failed, Incorrect Amount');
    }
  });

  it('payBill() should throw Error when amount passed is more than balance for PartialPayment payment status', async () => {
    const newBill = {
      ...billFactory.build(),
      senderProfile: {
        ...profileFactory.build(),
        type: UserType.OrganizationDoctor,
        fullName: 'qwert',
        hospital: { name: 'qwerty' },
      },
      receiverprofile: profileFactory.build(),
      totalAmount: 1000,
      discountAmount: 200,
      amountPayable: 800,
      amountPaid: 500,
      paymentStatus: FundTransferStatus.PartialPayment,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    try {
      await service.payBill(user.defaultProfile, {
        id: bill.id,
        amount: 400,
        passCode: 'correctPasscode',
      });
    } catch ({ message }) {
      expect(message).toEqual('Payment Failed, Incorrect Amount');
    }
  });

  it('payOrgBill() should pay a bill successfully when payment status is pending', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Pending,
      billStatus: BillStatus.PendingPayment,
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.save = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.payOrgBill(user.defaultProfile, {
      id: bill.id,
      amount: 950,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  it('payOrgBill() should pay a bill successfully when payment status is pending', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Pending,
      billStatus: BillStatus.PendingPayment,
      details: billDetailsFactory.buildList(2),
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.save = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.payOrgBill(user.defaultProfile, {
      id: bill.id,
      amount: 950,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  it('payOrgBill() should pay a bill successfully when payment status is pending and is not a split bill', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Pending,
      billStatus: BillStatus.PendingPayment,
      details: [
        {
          ...billDetailsFactory.build(),
          amount: 1000,
          discountAmount: 50,
          amountOutstanding: 0,
          amountPaid: 950,
        },
      ],
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.save = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.payOrgBill(user.defaultProfile, {
      id: bill.id,
      amount: 950,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  it('payOrgBill() should remove duplicate split bill item when bill is paid', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Pending,
      billStatus: BillStatus.PendingPayment,
      details: [
        {
          ...billDetailsFactory.build(),
          amount: 1000,
          discountAmount: 50,
          amountOutstanding: 0,
          amountPaid: 950,
        },
        {
          ...billDetailsFactory.build(),
          serviceType: 'Admission',
          amount: 1000,
          discountAmount: 50,
          amountOutstanding: 950,
          amountPaid: 0,
          excluded: false,
          splitReference: 'true',
        },
        {
          ...billDetailsFactory.build(),
          serviceType: 'Admission',
          amount: 1000,
          discountAmount: 50,
          amountOutstanding: 950,
          amountPaid: 0,
          excluded: false,
          splitReference: 'true',
        },
      ],
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.save = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.payOrgBill(user.defaultProfile, {
      id: bill.id,
      amount: 950,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  // eslint-disable-next-line max-len
  it('payOrgBill() should pay a bill successfully when payment status is pending and there is split bill with some amount paid', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Pending,
      billStatus: BillStatus.PendingPayment,
      details: [
        {
          ...billDetailsFactory.build(),
          amount: 1000,
          discountAmount: 50,
          amountOutstanding: 0,
          amountPaid: 950,
        },
        {
          ...billDetailsFactory.build(),
          splitReference: 'true',
          amount: 1500,
          discountAmount: 150,
          amountOutstanding: 1000,
          amountPaid: 350,
        },
      ],
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.save = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.payOrgBill(user.defaultProfile, {
      id: bill.id,
      amount: 950,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  it('payOrgBill() should pay a bill successfully when payment status is pending and there is split bill with no amount paid', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Pending,
      billStatus: BillStatus.PendingPayment,
      details: [
        {
          ...billDetailsFactory.build(),
          amount: 1000,
          discountAmount: 50,
          amountOutstanding: 0,
          amountPaid: 950,
        },
        {
          ...billDetailsFactory.build(),
          splitReference: 'true',
          amount: 1150,
          discountAmount: 150,
          amountOutstanding: 1000,
          amountPaid: 0,
        },
      ],
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.save = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.payOrgBill(user.defaultProfile, {
      id: bill.id,
      amount: 950,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  // eslint-disable-next-line max-len
  it('payOrgBill() should pay a bill successfully when payment status is pending and there is two split bill with no amount paid', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Pending,
      billStatus: BillStatus.PendingPayment,
      details: [
        {
          ...billDetailsFactory.build(),
          amount: 1000,
          discountAmount: 50,
          amountOutstanding: 0,
          amountPaid: 0,
          excluded: true,
        },
        {
          ...billDetailsFactory.build(),
          serviceType: 'Admission',
          splitReference: 'true',
          amount: 1150,
          discountAmount: 150,
          amountOutstanding: 1000,
          amountPaid: 1000,
        },
        {
          ...billDetailsFactory.build(),
          serviceType: 'Registration',
          splitReference: 'true',
          amount: 1150,
          discountAmount: 150,
          amountOutstanding: 1000,
          amountPaid: 0,
        },
        {
          ...billDetailsFactory.build(),
          serviceType: 'Registration',
          splitReference: 'true',
          amount: 1150,
          discountAmount: 150,
          amountOutstanding: 1000,
          amountPaid: 0,
        },
      ],
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.save = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    const billPayed = await service.payOrgBill(user.defaultProfile, {
      id: bill.id,
      amount: 950,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  it('payOrgBill() should throw error when bill amount has already been paid', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Completed,
      billStatus: BillStatus.Paid,
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);

    try {
      await service.payOrgBill(user.defaultProfile, {
        id: bill.id,
        amount: 400,
        passCode: 'correctPasscode',
      });
    } catch ({ message }) {
      expect(message).toEqual('Bill Already Paid');
    }
  });

  it('payOrgBill() should throw error when bill status is cancelled', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Cancelled,
      billStatus: BillStatus.Cancelled,
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    try {
      await service.payOrgBill(user.defaultProfile, {
        id: bill.id,
        amount: 400,
        passCode: 'correctPasscode',
      });
    } catch ({ message }) {
      expect(message).toEqual('Not Authorized To Pay Bill');
    }
  });

  it('payOrgBill() should throw error when bill status is cacelled with levy', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.CancelledWithLevy,
      billStatus: BillStatus.CancelledWithLevy,
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);
    try {
      await service.payOrgBill(user.defaultProfile, {
        id: bill.id,
        amount: 400,
        passCode: 'correctPasscode',
      });
    } catch ({ message }) {
      expect(message).toEqual('Not Authorized To Pay Bill');
    }
  });

  it('payOrgBill() should throw error when bill fails to save', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Pending,
      billStatus: BillStatus.PendingPayment,
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);

    mockBillRepository.save = jest
      .fn()
      .mockImplementationOnce(() => {
        throw new BadRequestException('Error: Expected uuid received false');
      })
      .mockImplementationOnce((v) => v)
      .mockImplementationOnce((v) => v);
    await expect(
      service.payOrgBill(user.defaultProfile, {
        id: bill.id,
        amount: 950,
        passCode: 'correctPasscode',
      }),
    ).rejects.toThrow('Error: Expected uuid received false');
  });

  it('payOrgBill() should throw error when bill fails to save', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      senderHospital: hospital,
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountOutstanding: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Pending,
      billStatus: BillStatus.PendingPayment,
    };

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.save = jest
      .fn()
      .mockRejectedValueOnce(
        new BadRequestException('Expected uuid received false'),
      )
      .mockImplementationOnce((v) => v)
      .mockImplementationOnce((v) => v);

    await expect(
      service.payOrgBill(user.defaultProfile, {
        id: bill.id,
        amount: 950,
        passCode: 'correctPasscode',
      }),
    ).rejects.toThrow('Expected uuid received false');
  });

  it('createBill() should throw error if bill with same reference exists', async () => {
    try {
      await service.createBill(profileFactory.build(), {
        ...bill,
        receiverProfileType: AccountHolder.User,
      });
    } catch ({ message }) {
      expect(mockBillRepository.bilRefExists).toBeCalled();
      expect(mockProfileRepository.byClinifyId).toBeCalled();
      expect(message).toEqual('Bill Not Found');
    }
  });

  it('createBill() should not verify password when the process is autotriggered', async () => {
    mockBillRepository.bilRefExists = jest
      .fn()
      .mockImplementationOnce(() => false);
    mockBillRepository.createBill = jest.fn(() => bill);
    const autoTrigger = true;
    const data = await service.createBill(
      userFactory.build(),
      { ...bill, receiverProfileType: AccountHolder.User },
      autoTrigger,
      managerMock,
    );
    expect(mockBillRepository.bilRefExists).toBeCalled();
    expect(mockProfileRepository.byClinifyId).toBeCalled();
    expect(data).toEqual(bill);
  });

  it('createBill() should successfully create a bill', async () => {
    mockBillRepository.bilRefExists = jest
      .fn()
      .mockImplementationOnce(() => false);
    const billData = { ...bill, receiverProfileType: AccountHolder.User };
    delete billData.discountAmount;
    const data = await service.createBill(userFactory.build(), billData);
    expect(mockBillRepository.bilRefExists).toBeCalled();
    expect(mockProfileRepository.byClinifyId).toBeCalled();
    expect(mockBillRepository.createBill).toBeCalled();
    expect(data).toEqual(bill);
  });

  it('createBill() should successfully create a bill when reference is not provided', async () => {
    const newData = {
      ...bill,
      reference: undefined,
      receiverProfileType: AccountHolder.User,
    };

    mockBillRepository.createBill = jest
      .fn()
      .mockImplementationOnce(() => newData);

    const data = await service.createBill(profileFactory.build(), newData);
    expect(mockBillRepository.bilRefExists).not.toBeCalled();
    expect(mockProfileRepository.byClinifyId).toBeCalled();
    expect(mockBillRepository.createBill).toBeCalled();
    expect(data).toEqual(newData);
  });

  it('createBill() should successfully throw error when receivers profile id is null', async () => {
    mockProfileRepository.byClinifyId = jest
      .fn()
      .mockImplementationOnce(() => null);

    mockBillRepository.bilRefExists = jest
      .fn()
      .mockImplementationOnce(() => false);

    try {
      await service.createBill(profileFactory.build(), {
        ...bill,
        receiverProfileType: AccountHolder.User,
      });
    } catch ({ message }) {
      expect(mockProfileRepository.byClinifyId).toBeCalled();
      expect(mockBillRepository.bilRefExists).toBeCalled();
      expect(message).toEqual('Patient Not Found');
    }

    mockProfileRepository.byClinifyId = jest.fn(() => user.defaultProfile);
  });

  it('createBill() should throw error when totalAmount is negative', async () => {
    const newData = {
      ...bill,
      totalAmount: -10,
      receiverProfileType: AccountHolder.User,
    };

    mockBillRepository.bilRefExists = jest
      .fn()
      .mockImplementationOnce(() => false);

    try {
      await service.createBill(profileFactory.build(), newData);
    } catch ({ message }) {
      expect(mockProfileRepository.byClinifyId).toBeCalled();
      expect(mockBillRepository.bilRefExists).toBeCalled();
      expect(message).toEqual('Amount Cannot Be Negative');
    }
  });

  it('createBill() should throw error when discountAmount is negative', async () => {
    const newData = {
      ...bill,
      discountAmount: -10,
      receiverProfileType: AccountHolder.User,
    };

    mockBillRepository.bilRefExists = jest
      .fn()
      .mockImplementationOnce(() => false);
    mockProfileRepository.byClinifyId = jest
      .fn()
      .mockImplementationOnce(() => ({
        ...profileFactory.build(),
        id: '123qwerty',
      }));
    try {
      await service.createBill(profileFactory.build(), newData);
    } catch ({ message }) {
      expect(mockProfileRepository.byClinifyId).toBeCalled();
      expect(mockBillRepository.bilRefExists).toBeCalled();
      expect(message).toEqual('Amount Cannot Be Negative');
    }
  });

  it('createBill() should throw an error if sender is also the receiver', async () => {
    mockBillRepository.bilRefExists = jest
      .fn()
      .mockImplementationOnce(() => false);
    const newBill = { ...bill, receiverProfileType: AccountHolder.User };
    mockProfileRepository.byClinifyId = jest
      .fn()
      .mockImplementationOnce(() => ({
        ...hospitalFactory.build(),
        id: 'testId',
      }));
    try {
      await service.createBill(
        { ...profileFactory.build(), id: 'testId' },
        newBill,
      );
    } catch ({ message }) {
      expect(mockBillRepository.bilRefExists).toBeCalled();
      expect(message).toEqual('You Cannot Send Bills To Self');
    }
  });

  it('createBill() should throw error when saving fails', async () => {
    mockBillRepository.createBill = jest.fn().mockImplementationOnce(() => {
      throw Error();
    });

    mockBillRepository.bilRefExists = jest
      .fn()
      .mockImplementationOnce(() => false);
    mockProfileRepository.byClinifyId = jest
      .fn()
      .mockImplementationOnce(() => ({
        ...profileFactory.build(),
        id: '123qwerty',
      }));
    try {
      await service.createBill(profileFactory.build(), {
        ...bill,
        receiverProfileType: AccountHolder.User,
      });
    } catch ({ message }) {
      expect(mockBillRepository.bilRefExists).toBeCalled();
      expect(mockProfileRepository.byClinifyId).toBeCalled();
      expect(message).toEqual('Error Creating Bill');
    }
  });

  it('getAllBills() should get all user bills', async () => {
    const { createdDate } = bill;
    const billsRecord = await service.getAllBills(user.defaultProfile, {
      skip: 0,
      take: 50,
      dateRange: {
        from: createdDate,
        to: createdDate,
      },
    });
    expect(billsRecord).toEqual(bills);
  });

  it('getAllBills() should throw error if fetching fails', async () => {
    const { createdDate } = bill;
    mockBillRepository.getBillsByClinifyId = jest
      .fn()
      .mockImplementationOnce(() => {
        throw Error();
      });
    try {
      await service.getAllBills(user.defaultProfile, {
        skip: 0,
        take: 50,
        dateRange: {
          from: createdDate,
          to: createdDate,
        },
      });
    } catch ({ message }) {
      expect(message).toEqual('Error Fetching Bills');
    }
  });

  it('getUnpaidBills() should get all unpaid user bills', async () => {
    const { createdDate } = bill;
    const billsRecord = await service.getUnpaidBills(user.defaultProfile, {
      skip: 0,
      take: 50,
      dateRange: {
        from: createdDate,
        to: createdDate,
      },
    });
    expect(billsRecord).toEqual(bills);
  });

  it('getUnpaidBills() should throw error if fetching fails', async () => {
    const { createdDate } = bill;
    mockBillRepository.getUnpaidBillsByClinifyId = jest
      .fn()
      .mockImplementationOnce(() => {
        throw Error();
      });
    try {
      await service.getUnpaidBills(user.defaultProfile, {
        skip: 0,
        take: 50,
        dateRange: {
          from: createdDate,
          to: createdDate,
        },
      });
    } catch ({ message }) {
      expect(message).toEqual('Error Fetching Bills');
    }
  });

  it('deleteBill() should delete user bills', async () => {
    mockBillRepository.updateBill = jest.fn().mockImplementationOnce(() => {
      throw Error();
    });
    try {
      await service.deleteBill(user.defaultProfile, 'password', bill.id);
    } catch ({ message }) {
      expect(message).toEqual('Error Deleting Bill');
    }
  });

  it('deleteBill() should delete user bills', async () => {
    const autoTriggered = true;
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill);
    const billRecord = await service.deleteBill(
      user.defaultProfile,
      'password',
      bill.id,
      autoTriggered,
    );
    expect(billRecord).toEqual(bill);
  });

  it('updateBill() should throw error if an update isnt sent', async () => {
    try {
      await service.updateBill(user.defaultProfile, '12345', bill.id, null);
    } catch ({ message }) {
      expect(message).toEqual('No Update Provided');
    }
  });

  it('updateBill() should throw error if an update isnt sent', async () => {
    try {
      await service.updateBill(user.defaultProfile, '12345', bill.id, {});
    } catch ({ message }) {
      expect(message).toEqual('No Update Provided');
    }
  });

  it('updateBill() should update bill successfully if reciever clinifyId is suppllied', async () => {
    const update = { ...billFactory.build(), receiverClinifyId: '12345' };
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill);
    mockTransactionProfileService.getTransactingProfileForFetching = jest
      .fn()
      .mockReturnValue({
        profileType: 'hospital employee',
        profile: { id: 'testId', clinifyId: '1234qwertt' },
      });
    delete update.id;
    const updatedBill = await service.updateBill(
      user.defaultProfile,
      '12345',
      bill.id,
      update,
    );
    expect(updatedBill).toEqual(bill);
  });

  it('addBillDetail() should call corresponding repository', async () => {
    await service.addBillDetail(user.defaultProfile, {}, 'new-nill-id');
    expect(mockBillRepository.addBillDetail).toHaveBeenCalledWith(
      user.defaultProfile,
      {},
      'new-nill-id',
      [],
    );
  });

  it('addBillDetail() should call corresponding repository in transaction', async () => {
    mockBillRepository.addBillDetail = jest.fn(() => bill);
    await service.addBillDetail(
      user.defaultProfile,
      {},
      'new-nill-id',
      managerMock,
    );
    expect(mockBillRepository.addBillDetail).toHaveBeenCalledWith(
      user.defaultProfile,
      {},
      'new-nill-id',
      [],
    );
  });

  it('updateBill() should update bill successfully', async () => {
    mockProfileRepository.byClinifyId = jest
      .fn()
      .mockImplementationOnce(() => profile);
    const update = billFactory.build();
    delete update.id;
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => update);
    const updatedBill = await service.updateBill(
      user.defaultProfile,
      '12345',
      bill.id,
      update,
    );
    expect(updatedBill).toEqual(update);
  });

  it('updateBill() should throw error if update fails', async () => {
    mockBillRepository.updateBill = jest.fn().mockImplementationOnce(() => {
      throw Error();
    });
    const update = billFactory.build();
    delete update.id;
    try {
      await service.updateBill(user.defaultProfile, '12345', bill.id, update);
    } catch ({ message }) {
      expect(message).toEqual('Error Updating Bill');
    }
  });

  it('updateBill() should throw BadRequestException error if updateBill in bill repository throws BadRequestException error', async () => {
    mockBillRepository.updateBill = jest.fn().mockImplementationOnce(() => {
      throw new BadRequestException('Cannot Modify Fulfilled Bills');
    });
    const update = billFactory.build();
    delete update.id;
    try {
      await service.updateBill(user.defaultProfile, '12345', bill.id, update);
    } catch (error) {
      expect(error).toBeInstanceOf(BadRequestException);
      expect(error.message).toEqual('Cannot Modify Fulfilled Bills');
    }
  });

  it('confirmBilledTransaction() should throw error if transfer hasnt been made', async () => {
    const newBill = billFactory.build();
    newBill.amount = 100;
    newBill.paymentStatus = FundTransferStatus.Pending;
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    try {
      await service.confirmBilledTransaction(
        user.defaultProfile,
        'password',
        bill.id,
      );
    } catch ({ message }) {
      expect(message).toEqual(
        'Error, Funds Not Transferred Before Transaction Confirmation',
      );
    }
  });

  it('confirmBilledTransaction() should confirm a payed bill', async () => {
    const profile = user.defaultProfile;
    profile.type = UserType.Patient;
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      amount: 100,
      deliveryConfirmation: DeliveryStatus.Pending,
      paymentStatus: FundTransferStatus.Completed,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    const confirmedBill = await service.confirmBilledTransaction(
      user.defaultProfile,
      'password',
      bill.id,
    );
    expect(confirmedBill.receiptConfirmation).toEqual(
      newBill.receiptConfirmation,
    );
  });

  it('confirmBilledTransaction() should confirm a payed bill with user profile', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      amount: 100,
      receiptConfirmation: ReceiptStatus.Pending,
      deliveryConfirmation: DeliveryStatus.Pending,
      paymentStatus: FundTransferStatus.Completed,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockProfileRepository.byClinifyId = jest
      .fn()
      .mockImplementationOnce(() => false);
    const profile = user.defaultProfile;
    profile.type = UserType.Doctor;
    const confirmedBill = await service.confirmBilledTransaction(
      user.defaultProfile,
      'password',
      bill.id,
    );
    expect(confirmedBill.deliveryConfirmation).toEqual(
      bill.deliveryConfirmation,
    );
  });

  it('confirmBilledTransaction() should confirm a payed bill with doctor profile', async () => {
    mockProfileRepository.byClinifyId = jest
      .fn()
      .mockImplementationOnce(() => false);
    const profile = user.defaultProfile;
    profile.type = UserType.Doctor;
    const newBill = billFactory.build();
    delete newBill.id;
    newBill.amount = 100;
    newBill.deliveryConfirmation = DeliveryStatus.Delivered;
    newBill.paymentStatus = FundTransferStatus.Completed;
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    const confirmedBill = await service.confirmBilledTransaction(
      user.defaultProfile,
      'password',
      bill.id,
    );
    expect(confirmedBill).toEqual(newBill);
  });

  it('payBill() should throw error if bill has increased microseconds before atmepting payment', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      amount: 1000,
      amountPayed: 0,
      paymentStatus: FundTransferStatus.Pending,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill);
    try {
      await service.payBill(user.defaultProfile, {
        id: bill.id,
        amount: 900,
        passCode: 'correctPasscode',
      });
    } catch ({ message }) {
      expect(message).toEqual('Payment Failed, Bill Amount Changed');
    }
  });

  it('payBill() should throw error if bill has been paid', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      amount: 1000,
      amountPayed: 0,
      paymentStatus: FundTransferStatus.Completed,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    try {
      await service.payBill(user.defaultProfile, {
        id: bill.id,
        amount: 900,
        passCode: 'correctPasscode',
      });
    } catch ({ message }) {
      expect(message).toEqual('Bill Already Paid');
    }
  });

  it('payBill() should throw error if transfer hasnt been made', async () => {
    const newBill = {
      ...billFactory.build(),
      receiverprofile: profileFactory.build(),
      senderProfile: profileFactory.build(),
      totalAmount: 1000,
      amountPaid: 0,
      discountAmount: 0,
      amountPayable: 1000,
      paymentStatus: FundTransferStatus.Pending,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.save = jest.fn().mockImplementationOnce(() => {
      throw new Error();
    });
    try {
      await service.payBill(user.defaultProfile, {
        id: bill.id,
        amount: 1000,
        passCode: 'correctPasscode',
      });
    } catch ({ message }) {
      expect(message).toEqual('Error Making Payment');
    }
  });

  it('payBill() should throw error if transfer has been made but validation failed', async () => {
    const newBill = {
      ...billFactory.build(),
      senderProfile: {
        ...profileFactory.build(),
        type: UserType.OrganizationDoctor,
        fullName: 'qwert',
        hospital: { name: 'qwerty' },
      },
      receiverprofile: profileFactory.build(),
      totalAmount: 1000,
      amountPaid: 500,
      discountAmount: 0,
      amountPayable: 1000,
      paymentStatus: FundTransferStatus.PartialPayment,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => {
        throw new Error();
      })
      .mockImplementationOnce(() => ({ ...newBill, amountPayable: 500 }));
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);

    try {
      await service.payBill(user.defaultProfile, {
        id: bill.id,
        amount: 500,
        passCode: 'correctPasscode',
      });
    } catch ({ message }) {
      expect(message).toEqual('Error Making Payment');
    }
  });

  it('payBill() should refund excess payment', async () => {
    const newBill = {
      ...billFactory.build(),
      senderProfile: {
        ...profileFactory.build(),
        type: UserType.OrganizationDoctor,
        fullName: 'qwert',
        hospital: { name: 'qwerty' },
      },
      receiverprofile: profileFactory.build(),
      totalAmount: 1000,
      amountPaid: 0,
      discountAmount: 200,
      amountPayable: 800,
      paymentStatus: FundTransferStatus.Pending,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => ({
        ...newBill,
        discountAmount: 400,
        amountPayable: 600,
        amountPaid: 800,
        deliveryConfirmation: DeliveryStatus.Delivered,
        receiptConfirmation: ReceiptStatus.Received,
        paymentStatus: FundTransferStatus.Completed,
      }));
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => ({
        ...bill,
        amountPaid: 800,
        paymentStatus: FundTransferStatus.Completed,
      }))
      .mockImplementationOnce(() => ({
        ...bill,
        amountPaid: 800,
        paymentStatus: FundTransferStatus.Completed,
      }))
      .mockImplementationOnce(() => ({
        ...newBill,
        discountAmount: 400,
        amountPayable: 600,
        amountPaid: 800,
        reversedAmount: 200,
        paymentStatus: FundTransferStatus.Overpayed,
      }));

    const billPayed = await service.payBill(user.defaultProfile, {
      id: bill.id,
      amount: 800,
      passCode: 'correctPasscode',
    });
    expect(billPayed.reversedAmount).toEqual(200);
    expect(billPayed.amountPayable).toEqual(600);
    expect(billPayed.amountPaid).toEqual(800);
    expect(billPayed.paymentStatus).toEqual(FundTransferStatus.Overpayed);
  });

  it('payBill() should return the bill unmanipulated if payment coditions are not met', async () => {
    const newBill = {
      ...billFactory.build(),
      senderProfile: {
        ...profileFactory.build(),
        type: UserType.OrganizationDoctor,
        fullName: 'qwert',
        hospital: { name: 'qwerty' },
      },
      receiverprofile: profileFactory.build(),
      totalAmount: 1000,
      amountPaid: 5000,
      discountAmount: 0,
      amountPayable: 1000,
      paymentStatus: FundTransferStatus.Cancelled,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => ({ ...newBill, amount: 500 }));
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => ({ ...bill, amountPayed: 500 }))
      .mockImplementationOnce(() => bill);

    const billPayed = await service.payBill(user.defaultProfile, {
      id: bill.id,
      amount: 1000,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(newBill);
  });

  it('payBill() should throw error when user passcode is wrong', async () => {
    const newBill = {
      ...billFactory.build(),
      senderProfile: {
        ...profileFactory.build(),
        type: UserType.OrganizationDoctor,
        fullName: 'qwert',
        hospital: { name: 'qwerty' },
      },
      receiverprofile: profileFactory.build(),
      amount: 100,
    };

    mockValidatePasscodeService.validatePasscode = jest.fn(() => {
      throw new Error('Cannot Verify Patient. Please, Try Again');
    });

    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill)
      .mockImplementationOnce(() => newBill);
    mockBillRepository.updateBill = jest
      .fn()
      .mockImplementationOnce(() => bill)
      .mockImplementationOnce(() => bill);

    try {
      await service.payBill(user.defaultProfile, {
        id: bill.id,
        amount: 1000,
        passCode: 'wrongPasscode',
      });
    } catch ({ message }) {
      expect(mockValidatePasscodeService.validatePasscode).toBeCalledTimes(1);
      expect(message).toEqual('Cannot Verify Patient. Please, Try Again');
    }

    mockValidatePasscodeService.validatePasscode = jest.fn();
  });
  it('cancelBillOrChargeLevy() should cancle a bill successfully by hospital', async () => {
    mockTransactionProfileService.getTransactingProfile = jest
      .fn()
      .mockImplementationOnce(() => ({
        profileType: 'hospital',
        hospital: { id: 'testId', clinifyId: '1234qwertt' },
      }));
    const receiverHospital = { ...hospitalFactory.build(), id: 'testId' };
    const newBill = {
      ...billFactory.build(),
      receiverHospital,
      senderHospital: hospitalFactory.build(),
      totalAmount: 1000,
      discountAmount: 50,
      amountPayable: 950,
      amountPaid: 0,
      paymentStatus: FundTransferStatus.Completed,
    };
    mockBillRepository.getBill = jest
      .fn()
      .mockImplementationOnce(() => newBill);
    mockBillRepository.updateBill = jest.fn(() => bill);

    const billPayed = await service.cancelBillOrChargeLevy({
      profile,
      billId: bill.id,
      passCode: 'correctPasscode',
    });
    expect(billPayed).toEqual(bill);
  });

  it('createBill() should successfully create a bill by organisation', async () => {
    mockTransactionProfileService.getTransactingProfile = jest
      .fn()
      .mockImplementationOnce(() => ({
        profileType: 'hospital',
        hospital: { id: 'testId', clinifyId: '1234qwertt' },
      }));
    mockBillRepository.bilRefExists = jest
      .fn()
      .mockImplementationOnce(() => false);
    mockBillRepository.createBill = jest
      .fn()
      .mockImplementationOnce(() => bill);
    const billData = { ...bill, receiverProfileType: AccountHolder.Hospital };
    delete billData.discountAmount;
    const data = await service.createBill(
      {
        ...userFactory.build(),
        type: UserType.OrganizationDoctor,
      },
      billData,
    );
    expect(mockBillRepository.bilRefExists).toBeCalled();
    expect(mockHospitalRepository.byClinifyId).toBeCalled();
    expect(mockBillRepository.createBill).toBeCalled();
    expect(data).toEqual(bill);
  });

  it('getAllUserBills() should get user bills', async () => {
    const userBills = await service.getAllUserBills('user-profile-id', {
      skip: 0,
      take: 20,
    });
    expect(mockBillRepository.findBillByProfile).toBeCalled();
    expect(mockBillRepository.findBillByProfile).toHaveBeenLastCalledWith(
      'user-profile-id',
      { skip: 0, take: 20 },
    );
    expect(userBills.list[0]).toEqual(bill);
  });

  it('getAllHospitalBills() should get hospital bills', async () => {
    const hospitalBills = await service.getAllHospitalBills(
      profile,
      'hospital-id',
      {
        skip: 0,
        take: 20,
      },
    );
    expect(mockBillRepository.findBillByHospital).toBeCalled();
    expect(mockBillRepository.findBillByHospital).toHaveBeenLastCalledWith(
      profile,
      'hospital-id',
      { skip: 0, take: 20 },
    );
    expect(hospitalBills.list[0]).toEqual(bill);
  });

  it('getPatientBills(): should get patient bills', async () => {
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationCashier;
    await service.getPatientBills(profile, profile);
    expect(mockBillRepository.getPatientBills).toHaveBeenCalled();
  });

  it('getPatientBills(): should get patient bills', async () => {
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    await expect(service.getPatientBills(profile, profile)).rejects.toThrow(
      'Not Authorized To View This Record',
    );
  });

  it('getHospitalBills(): should get hospital bills', async () => {
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationCashier;
    await service.getHospitalBills(user.defaultProfile?.hospital, {});
    expect(mockBillRepository.getHospitalBills).toHaveBeenCalled();
  });

  it('getHospitalBills(): manages error', async () => {
    const profile = profileFactory.build();
    profile.type = UserType.OrganizationDoctor;
    mockBillRepository.getHospitalBills = jest.fn().mockRejectedValueOnce({});
    await expect(
      service.getHospitalBills(user.defaultProfile?.hospital, profile),
    ).rejects.toThrow('Error Fetching Bills');
  });

  it('createBill() should throw error if recieiverProfileType is not provided', async () => {
    mockTransactionProfileService.getTransactingProfile = jest
      .fn()
      .mockImplementationOnce(() => ({
        profileType: 'hospital',
        hospital: { id: 'testId', clinifyId: '1234qwertt' },
      }));
    mockBillRepository.bilRefExists = jest
      .fn()
      .mockImplementationOnce(() => false);
    mockBillRepository.createBill = jest
      .fn()
      .mockImplementationOnce(() => bill);
    const billData = bill;
    delete billData.discountAmount;
    try {
      await service.createBill(
        {
          ...userFactory.build(),
          type: UserType.OrganizationDoctor,
        },
        billData,
      );
    } catch ({ message }) {
      expect(message).toEqual('Receiver Profile Type Is Required');
    }
  });
  it('addOrgBill() should call the addOrgBill repository', async () => {
    mockBillRepository.cancelOrgBill = jest.fn(() => bill);
    const newUser = {
      ...userFactory.build(),
      type: UserType.OrganizationDoctor,
    };
    const response = await service.addOrgBill(newUser.defaultProfile, bill);

    expect(response).toStrictEqual(bill);
  });
  it('addOrgBill() should call the addOrgBill repository', async () => {
    mockBillRepository.cancelOrgBill = jest.fn(() => bill);
    const newBill = {
      ...bill,
      details: billDetailsFactory.buildList(2),
    };
    const newUser = {
      ...userFactory.build(),
      type: UserType.OrganizationDoctor,
    };
    const response = await service.addOrgBill(newUser.defaultProfile, newBill, {
      billDetailId: newBill.details[1].id,
      amount: 400000,
      senderClinifyId: 'patient-clinify-id',
      receiverClinifyId: 'hospital-clinify-id',
      phoneNumber: '23467890876589',
      otpCode: '5623',
      transactionReference: 'new-transaction-reference',
    });
    expect(response).toStrictEqual(bill);
  });
  it('addOrgBill() should throw error when wallet bill detail id is not provided', async () => {
    mockBillRepository.cancelOrgBill = jest.fn(() => bill);
    const newUser = {
      ...userFactory.build(),
      type: UserType.OrganizationDoctor,
    };
    await expect(
      service.addOrgBill(newUser.defaultProfile, bill, {
        billDetailId: '',
        amount: 400000,
        senderClinifyId: 'patient-clinify-id',
        receiverClinifyId: 'hospital-clinify-id',
        phoneNumber: '23467890876589',
        otpCode: '5623',
        transactionReference: 'new-transaction-reference',
      }),
    ).rejects.toThrow('Wallet Bill ID Not Found');
  });
  it('addOrgBill() should throw error when wallet bill detail id is not found in bill details', async () => {
    mockBillRepository.cancelOrgBill = jest.fn(() => bill);
    const newUser = {
      ...userFactory.build(),
      type: UserType.OrganizationDoctor,
    };
    await expect(
      service.addOrgBill(
        newUser.defaultProfile,
        {
          ...bill,
          details: [
            {
              id: 'another-bill-detail-id',
              amountOutstanding: 40000,
              amountPaid: 0,
              amount: 40000,
            },
          ],
        },
        {
          billDetailId: 'bill-detail-id',
          amount: 400000,
          senderClinifyId: 'patient-clinify-id',
          receiverClinifyId: 'hospital-clinify-id',
          phoneNumber: '23467890876589',
          otpCode: '5623',
          transactionReference: 'new-transaction-reference',
        },
      ),
    ).rejects.toThrow('Wallet Bill ID Not Found');
  });
  it('updateOrgBill() should call the updateOrgBill repository', async () => {
    const newUser = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    mockBillRepository.save = jest.fn().mockImplementationOnce(() => bill);
    const response = await service.updateOrgBill('existing-bill-id', newUser, {
      ...bill,
      amountOverpaid: 1000,
    });

    expect(response).toStrictEqual({
      ...bill,
      receiverProfile: { ...bill.receiverProfile, user: expect.anything() },
    });
  });
  it('updateOrgBill() should deduct from deposit when payment type is deposit', async () => {
    const input = {
      ...bill,
      details: [
        { ...bill.details[0], paymentType: 'Deposit', amountPaid: 100 },
      ],
      amountUnderpaid: 1000,
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
        details: [
          { ...bill.details[0], paymentType: 'Deposit', amountPaid: 0 },
        ],
      }),
    );
    mockBillRepository.save.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
      }),
    );
    await service.updateOrgBill('id', mutator, input);
    expect(
      MockPaymentDepositService.makeServicePaymentFromDeposit,
    ).toHaveBeenCalledWith(
      mutator,
      input.receiverProfileId,
      100,
      expect.anything(),
      expect.anything(),
      expect.anything(),
      true,
      undefined,
    );
    expect(pubSubMock.publish).toHaveBeenCalledWith('PaymentDepositAdded', {
      PaymentDepositAdded: mockPaymentDeposit,
    });
  });
  it('updateOrgBill() should deduct from deposit when payment type is deposit even when no existing item', async () => {
    const input = {
      ...bill,
      details: [
        {
          ...bill.details[0],
          paymentType: 'Deposit',
          amountPaid: 100,
        },
        {
          ...bill.details[0],
          paymentType: 'Cash',
          amountPaid: 0,
          additionalPayments: [
            { splitPaymentType: 'Deposit', splitAmount: 500 },
          ],
        },
      ],
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
        details: [
          {
            ...bill.details[0],
            id: chance.guid({ version: 4 }),
            paymentType: 'Cash',
            amountPaid: 0,
          },
        ],
      }),
    );
    mockBillRepository.save.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
      }),
    );
    await service.updateOrgBill('id', mutator, input);
    expect(
      MockPaymentDepositService.makeServicePaymentFromDeposit,
    ).toHaveBeenCalledWith(
      mutator,
      input.receiverProfileId,
      600,
      expect.anything(),
      expect.anything(),
      expect.anything(),
      true,
      undefined,
    );
    expect(pubSubMock.publish).toHaveBeenCalledWith('PaymentDepositAdded', {
      PaymentDepositAdded: mockPaymentDeposit,
    });
  });
  it('updateOrgBill() should refund to deposit when payment type is deposit and update is amount paid is reduced', async () => {
    const _billDetails = billDetailsFactory.build();
    const input = {
      ...bill,
      details: [
        { ...bill.details[0], paymentType: 'Deposit', amountPaid: 60 },
        {
          ..._billDetails,
          paymentType: 'Cheque',
          amountPaid: 0,
          additionalPayments: [
            { splitPaymentType: 'Deposit', splitAmount: 100 },
          ],
        },
      ],
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
        details: [
          { ...bill.details[0], paymentType: 'Deposit', amountPaid: 100 },
          {
            ..._billDetails,
            paymentType: 'Cheque',
            amountPaid: 0,
            additionalPayments: [
              { splitPaymentType: 'Deposit', splitAmount: 200 },
            ],
          },
        ],
      }),
    );
    mockBillRepository.save.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
      }),
    );
    await service.updateOrgBill('id', mutator, input);
    expect(MockPaymentDepositService.addPaymentDeposit).toHaveBeenCalledWith(
      mutator,
      expect.objectContaining({
        amountUsed: 0,
        amountDeposited: 140,
        profileId: bill.receiverProfileId,
      }),
      expect.anything(),
      true,
    );
    expect(pubSubMock.publish).toHaveBeenCalledWith('PaymentDepositAdded', {
      PaymentDepositAdded: mockPaymentDeposit,
    });
  });
  it('updateOrgBill() should refund from deposit when item payment type is deposit is deleted', async () => {
    const initialBill = {
      ...bill,
      details: [
        { ...bill.details[0], paymentType: 'Deposit', amountPaid: 60 },
        {
          ...bill.details[0],
          id: chance.guid({ version: 4 }),
          paymentType: 'Cash',
        },
        {
          ...bill.details[0],
          paymentType: 'Cash',
          amountPaid: 0,
          additionalPayments: [
            { splitPaymentType: 'Deposit', splitAmount: 100 },
          ],
        },
      ],
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    const input = {
      ...bill,
      details: [
        {
          ...bill.details[0],
          id: chance.guid({ version: 4 }),
          paymentType: 'Cash',
        },
      ],
    };
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(initialBill),
    );
    mockBillRepository.save.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
      }),
    );
    await service.updateOrgBill('id', mutator, input);
    expect(MockPaymentDepositService.addPaymentDeposit).toHaveBeenCalledWith(
      mutator,
      expect.objectContaining({
        amountUsed: 0,
        amountDeposited: 160,
        profileId: bill.receiverProfileId,
      }),
      expect.anything(),
      true,
    );
    expect(pubSubMock.publish).toHaveBeenCalledWith('PaymentDepositAdded', {
      PaymentDepositAdded: mockPaymentDeposit,
    });
  });
  it('updateOrgBill() should refund from deposit when item payment type is deposit is deleted (sub bill)', async () => {
    const initialBill = {
      ...bill,
      details: [
        {
          ...bill.details[0],
          paymentType: 'Cash',
          subBills: [
            {
              ...bill.details[0],
              id: chance.guid({ version: 4 }),
              paymentType: 'Deposit',
              amountPaid: 60,
            },
          ],
        },
      ],
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    const input = {
      ...bill,
      details: [
        {
          ...bill.details[0],
          paymentType: 'Cash',
          subBills: [],
        },
      ],
    };
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve(initialBill),
    );
    mockBillRepository.save.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
      }),
    );
    await service.updateOrgBill('id', mutator, input);
    expect(MockPaymentDepositService.addPaymentDeposit).toHaveBeenCalledWith(
      mutator,
      expect.objectContaining({
        amountUsed: 0,
        amountDeposited: 60,
        profileId: bill.receiverProfileId,
      }),
      expect.anything(),
      true,
    );
    expect(pubSubMock.publish).toHaveBeenCalledWith('PaymentDepositAdded', {
      PaymentDepositAdded: mockPaymentDeposit,
    });
  });
  it('updateOrgBill() should deduct from deposit when payment type is deposit and amount paid is more than initial payment (sub bill)', async () => {
    const input = {
      ...bill,
      details: [
        {
          ...bill.details[0],
          paymentType: 'Cash',
          amountPaid: 100,
          subBills: [
            {
              ...bill.details[0],
              paymentType: 'Deposit',
              amountPaid: 240,
            },
          ],
        },
      ],
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
        details: [
          {
            ...bill.details[0],
            paymentType: 'Cash',
            amountPaid: 100,
            subBills: [
              {
                ...bill.details[0],
                paymentType: 'Deposit',
                amountPaid: 100,
              },
            ],
          },
        ],
      }),
    );
    mockBillRepository.save.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
      }),
    );
    await service.updateOrgBill('id', mutator, input);
    expect(
      MockPaymentDepositService.makeServicePaymentFromDeposit,
    ).toHaveBeenCalledWith(
      mutator,
      input.receiverProfileId,
      140,
      expect.anything(),
      expect.anything(),
      expect.anything(),
      true,
      undefined,
    );
    expect(pubSubMock.publish).toHaveBeenCalledWith('PaymentDepositAdded', {
      PaymentDepositAdded: mockPaymentDeposit,
    });
  });
  it('updateOrgBill() should deduct from deposit when payment type is deposit (sub bill)', async () => {
    const input = {
      ...bill,
      details: [
        {
          ...bill.details[0],
          paymentType: 'Cash',
          amountPaid: 100,
          subBills: [
            {
              ...bill.details[0],
              paymentType: 'Deposit',
              amountPaid: 200,
            },
          ],
        },
      ],
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
        details: [{ ...bill.details[0], paymentType: 'Cash', amountPaid: 100 }],
      }),
    );
    mockBillRepository.save.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
      }),
    );
    await service.updateOrgBill('id', mutator, input);
    expect(
      MockPaymentDepositService.makeServicePaymentFromDeposit,
    ).toHaveBeenCalledWith(
      mutator,
      input.receiverProfileId,
      200,
      expect.anything(),
      expect.anything(),
      expect.anything(),
      true,
      undefined,
    );
    expect(pubSubMock.publish).toHaveBeenCalledWith('PaymentDepositAdded', {
      PaymentDepositAdded: mockPaymentDeposit,
    });
  });
  it('updateOrgBill() should refund to deposit when payment type is deposit and update is amount paid is reduced (sub bill)', async () => {
    const input = {
      ...bill,
      details: [
        {
          ...bill.details[0],
          paymentType: 'Cash',
          amountPaid: 100,
          subBills: [
            {
              ...bill.details[0],
              paymentType: 'Deposit',
              amountPaid: 120,
            },
          ],
        },
      ],
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
        details: [
          {
            ...bill.details[0],
            paymentType: 'Cash',
            amountPaid: 100,
            subBills: [
              {
                ...bill.details[0],
                paymentType: 'Deposit',
                amountPaid: 200,
              },
            ],
          },
        ],
      }),
    );
    mockBillRepository.save.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
      }),
    );
    await service.updateOrgBill('id', mutator, input);
    expect(MockPaymentDepositService.addPaymentDeposit).toHaveBeenCalledWith(
      mutator,
      expect.objectContaining({
        amountUsed: 0,
        amountDeposited: 80,
        profileId: bill.receiverProfileId,
      }),
      expect.anything(),
      true,
    );
    expect(pubSubMock.publish).toHaveBeenCalledWith('PaymentDepositAdded', {
      PaymentDepositAdded: mockPaymentDeposit,
    });
  });
  it('updateOrgBill() should throw when payment made from deposit conflicts with change', async () => {
    const input = {
      ...bill,
      details: [
        {
          ...bill.details[0],
          paymentType: 'Cash',
          amountPaid: 120,
        },
      ],
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
        details: [
          {
            ...bill.details[0],
            paymentType: 'Deposit',
            amountPaid: 100,
          },
        ],
      }),
    );
    await expect(service.updateOrgBill('id', mutator, input)).rejects.toThrow(
      'Bill Includes Conflicting Payment Type',
    );
  });
  it('updateOrgBill() should throw when payment made from deposit conflicts with change (sub bill)', async () => {
    const input = {
      ...bill,
      details: [
        {
          ...bill.details[0],
          paymentType: 'Cash',
          amountPaid: 100,
          subBills: [
            {
              ...bill.details[0],
              paymentType: 'Cash',
              amountPaid: 120,
            },
          ],
        },
      ],
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
        details: [
          {
            ...bill.details[0],
            paymentType: 'Cash',
            amountPaid: 100,
            subBills: [
              {
                ...bill.details[0],
                paymentType: 'Deposit',
                amountPaid: 200,
              },
            ],
          },
        ],
      }),
    );
    await expect(service.updateOrgBill('id', mutator, input)).rejects.toThrow(
      'Bill Includes Conflicting Payment Type',
    );
  });
  it('updateOrgBill() should throw when there are more than one payment deposit', async () => {
    const input = {
      ...bill,
      details: [
        {
          ...bill.details[0],
          paymentType: 'Deposit',
          amountPaid: 120,
          additionalPayments: [
            { splitPaymentType: 'Deposit', splitAmount: 100 },
          ],
        },
      ],
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
        details: [
          {
            ...bill.details[0],
            paymentType: 'Deposit',
            amountPaid: 100,
          },
        ],
      }),
    );
    await expect(service.updateOrgBill('id', mutator, input)).rejects.toThrow(
      'You Cannot Have More Than One Deposit Payment',
    );
  });
  it('updateOrgBill() should throw when there are more than one payment deposit', async () => {
    const input = {
      ...bill,
      details: [
        {
          ...bill.details[0],
          paymentType: 'Cash',
          amountPaid: 120,
          additionalPayments: [
            { splitPaymentType: 'Deposit', splitAmount: 80 },
            { splitPaymentType: 'Cheque', splitAmount: 500 },
            { splitPaymentType: 'Deposit', splitAmount: 230 },
          ],
        },
      ],
    };
    const mutator = {
      ...profile,
      type: UserType.OrganizationCashier,
    } as ProfileModel;
    mockBillRepository.findOneOrFail.mockImplementationOnce(() =>
      Promise.resolve({
        ...bill,
        details: [
          {
            ...bill.details[0],
            paymentType: 'Cash',
            amountPaid: 100,
          },
        ],
      }),
    );
    await expect(service.updateOrgBill('id', mutator, input)).rejects.toThrow(
      'You Cannot Have More Than One Deposit Payment',
    );
  });

  it('deleteOrgBills() should call the deleteOrgBills repository', async () => {
    const newUser = {
      ...userFactory.build(),
      type: UserType.OrganizationDoctor,
    };
    const response = await service.deleteOrgBills(
      ['existing-bill-id', 'another-bill-id'],
      newUser.defaultProfile,
    );

    expect(mockBillRepository.deleteOrgBills).toHaveBeenCalledTimes(1);
    expect(mockBillRepository.deleteOrgBills).toHaveBeenLastCalledWith(
      ['existing-bill-id', 'another-bill-id'],
      newUser.defaultProfile,
    );
    expect(response).toStrictEqual(bill);
  });
  it('archiveOrgBills() should call the archiveBills repository', async () => {
    const newUser = {
      ...userFactory.build(),
      type: UserType.OrganizationDoctor,
    };
    const response = await service.archiveOrgBills(
      ['existing-bill-id', 'another-bill-id'],
      newUser.defaultProfile,
      false,
    );

    expect(mockBillRepository.archiveBills).toHaveBeenCalledTimes(1);
    expect(mockBillRepository.archiveBills).toHaveBeenLastCalledWith(
      ['existing-bill-id', 'another-bill-id'],
      newUser.defaultProfile,
      false,
    );
    expect(response).toStrictEqual(bill);
  });

  it('archiveBillsR() should all the archiveBillsR service', async () => {
    await service.archiveBillsR(['existing-bill-id', 'another-bill-id'], true);
    expect(mockBillRepository.archiveBillsR).toHaveBeenCalledWith(
      ['existing-bill-id', 'another-bill-id'],
      true,
    );
  });

  it('cancelOrgBill() should call the cancelOrgBill repository', async () => {
    const newUser = {
      ...userFactory.build(),
      type: UserType.OrganizationDoctor,
    };
    const response = await service.cancelOrgBill(
      'existing-bill-id',
      newUser.defaultProfile,
    );

    expect(mockBillRepository.cancelOrgBill).toHaveBeenCalledTimes(1);
    expect(mockBillRepository.cancelOrgBill).toHaveBeenLastCalledWith(
      'existing-bill-id',
      newUser.defaultProfile,
    );
    expect(response).toStrictEqual(bill);
  });

  it('unCancelOrgBill() should call the cancelOrgBill repository', async () => {
    const newUser = {
      ...userFactory.build(),
      type: UserType.OrganizationDoctor,
    };
    const response = await service.unCancelOrgBill(
      'existing-bill-id',
      newUser.defaultProfile,
    );

    expect(mockBillRepository.unCancelOrgBill).toHaveBeenCalledTimes(1);
    expect(mockBillRepository.unCancelOrgBill).toHaveBeenLastCalledWith(
      'existing-bill-id',
      newUser.defaultProfile,
    );
    expect(response).toStrictEqual(bill);
  });
  it('recallOrgBill() should call the recallOrgBill repository', async () => {
    const newUser = {
      ...userFactory.build(),
      type: UserType.OrganizationDoctor,
    };
    const response = await service.recallOrgBill(
      'existing-bill-id',
      newUser.defaultProfile,
      false,
    );

    expect(mockBillRepository.recallOrgBill).toHaveBeenCalledTimes(1);
    expect(mockBillRepository.recallOrgBill).toHaveBeenLastCalledWith(
      'existing-bill-id',
      newUser.defaultProfile,
      false,
    );
    expect(response).toStrictEqual(bill);
  });

  it('generateBill() should return null when mutator profile type is patient', async () => {
    mockBillRepository.cancelOrgBill = jest.fn(() => bill);
    mockBillRepository.findOne = jest.fn(() => bill);
    managerMock.findOne = jest
      .fn()
      .mockReturnValueOnce(null)
      .mockReturnValue(bill.details);
    profile.type = UserType.Patient;
    profile.hospital = hospital;
    const generatedBill = await service.generateBill(
      profile,
      user.defaultProfile,
      {},
      ServiceType.Admission,
      'yu7tyu8iu',
      managerMock,
    );

    expect(generatedBill).toBeFalsy();
  });

  it('generateBill() should generate bill', async () => {
    mockBillRepository.cancelOrgBill = jest.fn(() => bill);
    mockBillRepository.findOne = jest.fn(() => bill);
    managerMock.findOne = jest
      .fn()
      .mockReturnValueOnce(null)
      .mockReturnValue(bill.details);
    profile.type = UserType.OrganizationDoctor;
    profile.hospital = hospital;
    const generatedBill = await service.generateBill(
      profile,
      user.defaultProfile,
      {},
      ServiceType.Admission,
      'yu7tyu8iu',
      managerMock,
    );

    expect(generatedBill).toEqual(bill);
  });

  it('generateBill() should return undefined when bill item already exist', async () => {
    mockBillRepository.cancelOrgBill = jest.fn(() => bill);
    mockBillRepository.findOne = jest.fn(() => bill);
    managerMock.findOne = jest.fn().mockReturnValue(bill.details);
    profile.type = UserType.OrganizationDoctor;
    profile.hospital = hospital;
    const generatedBill = await service.generateBill(
      profile,
      user.defaultProfile,
      {},
      ServiceType.Admission,
      'yu7tyu8iu',
      managerMock,
    );

    expect(generatedBill).toBeFalsy();
  });

  it('generateBill() should generate bill for Laboratory result', async () => {
    const newBillDetails = billDetailsFactory.build();
    mockBillRepository.cancelOrgBill = jest.fn(() => bill);
    mockBillRepository.findOne = jest.fn(() => bill);
    mockBillRepository.addBillDetail = jest.fn(() => newBillDetails);
    managerMock.findOne = jest
      .fn()
      .mockReturnValueOnce(null)
      .mockReturnValue(bill.details);
    profile.type = UserType.OrganizationDoctor;
    profile.hospital = hospital;
    const generatedBill = await service.generateBill(
      profile,
      user.defaultProfile,
      {},
      ServiceType.Laboratory,
      'yu7tyu8iu',
      managerMock,
    );

    expect(generatedBill).toEqual({
      ...bill,
      details: [...bill.details, newBillDetails],
    });
  });

  it('generateBill() should generate bill for Radiology exam', async () => {
    const newBillDetails = billDetailsFactory.build();

    mockBillRepository.findOne = jest.fn().mockResolvedValue(bill);
    mockBillRepository.createOrgBill = jest.fn().mockResolvedValue(bill);
    mockBillRepository.addBillDetail = jest.fn(() => newBillDetails);
    managerMock.findOne = jest
      .fn()
      .mockReturnValueOnce(null)
      .mockReturnValue(bill.details);
    profile.type = UserType.OrganizationDoctor;
    profile.hospital = hospital;
    const generatedBill = await service.generateBill(
      profile,
      user.defaultProfile,
      {},
      ServiceType.Radiology,
      'yu7tyu8iu',
      managerMock,
    );

    expect(generatedBill).toEqual({
      ...bill,
      details: [...bill.details, newBillDetails],
    });
  });

  it('updateBillDetails() should update Bill Details', async () => {
    const newBill = {
      ...bill,
      details: billDetailsFactory.buildList(2),
      amountUnderpaid: 1000,
    };
    mockBillRepository.findOneOrFail = jest.fn().mockResolvedValue(newBill);

    await service.updateBillDetails(profile, 'bill-id', newBill.details);
  });

  it('updateBillDetails() should update Bill Details with new detail', async () => {
    const newBill = {
      ...bill,
      details: billDetailsFactory.buildList(2),
      amountOverpaid: 1000,
    };
    mockBillRepository.findOneOrFail = jest
      .fn()
      .mockResolvedValue({ ...bill, amountOverpaid: 1000 });
    await service.updateBillDetails(profile, 'bill-id', newBill.details);
  });

  it('updateBillDetails() should update Bill Details with wallet', async () => {
    const newBill = {
      ...bill,
      details: billDetailsFactory.buildList(2),
    };
    mockBillRepository.findOneOrFail = jest.fn().mockResolvedValue(newBill);

    await service.updateBillDetails(profile, 'bill-id', newBill.details, {
      billDetailId: newBill.details[0].id,
      amount: 400000,
      senderClinifyId: 'patient-clinify-id',
      receiverClinifyId: 'hospital-clinify-id',
      phoneNumber: '23467890876589',
      otpCode: '5623',
      transactionReference: 'new-transaction-reference',
    });
    expect(mockWalletService.chargePatientWallet).toHaveBeenCalled();
  });

  it('updateBillDetails() should throw error when bill details to charge is not found', async () => {
    const newBill = {
      ...bill,
      details: billDetailsFactory.buildList(2),
    };
    mockBillRepository.findOneOrFail = jest.fn().mockResolvedValue(newBill);

    await expect(
      service.updateBillDetails(profile, 'bill-id', newBill.details, {
        billDetailId: 'non-existing-bill-detail-id',
        amount: 400000,
        senderClinifyId: 'patient-clinify-id',
        receiverClinifyId: 'hospital-clinify-id',
        phoneNumber: '23467890876589',
        otpCode: '5623',
        transactionReference: 'new-transaction-reference',
      }),
    ).rejects.toThrowError('Wallet Bill ID Not Found');
  });

  it('updateBillDetails() should throw if Bill Not Found', async () => {
    mockBillRepository.findOneOrFail = jest.fn().mockRejectedValue('');
    await expect(
      service.updateBillDetails(profile, 'bill-id', bill),
    ).rejects.toThrow('Bill Not Found');
  });

  it('generateBill() should generate bill on medication dispense with no sub service type', async () => {
    mockBillRepository.findOne = jest.fn().mockResolvedValue(bill);
    mockBillRepository.createOrgBill = jest.fn().mockResolvedValue(bill);
    mockBillRepository.addBillDetail = jest.fn(() => billDetails);

    managerMock.findOne = jest
      .fn()
      .mockReturnValueOnce(null)
      .mockReturnValue(bill.details);
    profile.type = UserType.OrganizationNurse;
    profile.hospital = hospital;
    const generatedBill = await service.generateBill(
      profile,
      user.defaultProfile,
      dispenseDetailsfactory.build(),
      ServiceType.Medication,
      undefined,
      managerMock,
      undefined,
      bill,
    );

    expect(generatedBill).toEqual({
      ...bill,
      details: [...bill.details, billDetails],
    });
  });

  it('generateBill() should generate bill on medication dispense with sub service type', async () => {
    const newBillDetail = [
      {
        ...billDetails,
        serviceType: ServiceType.Medication,
        subServiceType: '2 x 05% Methanol',
      },
      {
        ...billDetails,
        serviceType: ServiceType.Medication,
        subServiceType: '1 x 05% Methanol',
      },
      {
        ...billFactory.build(),
        serviceType: ServiceType.Medication,
        subServiceType: '2 x 045% Benatholyn',
      },
    ];
    managerMock.getCustomRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn().mockResolvedValue({
        ...bill,
        details: [...bill.details, ...newBillDetail],
      }),
      createOrgBill: jest.fn().mockResolvedValue(bill),
      addBillDetail: jest.fn(() => billDetails),
    }));
    managerMock.findOne = jest
      .fn()
      .mockReturnValueOnce(null)
      .mockReturnValue(bill.details);
    profile.type = UserType.OrganizationNurse;
    profile.hospital = hospital;
    const generatedBill = await service.generateBill(
      profile,
      user.defaultProfile,
      dispenseDetailsfactory.build(),
      ServiceType.Medication,
      undefined,
      managerMock,
      '05% Methanol',
    );

    expect(generatedBill).toBeTruthy();
  });

  it('deleteAutoGeneratedBill() should delete auto-generated bill', async () => {
    managerMock.createQueryBuilder = jest.fn().mockImplementation(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      returning: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      execute: jest.fn(() => ({
        raw: [
          {
            bill: 'bill',
            amount: 0,
            amount_outstanding: 0,
            discount_amount: 0,
            amount_owing: 0,
          },
        ],
        affected: 1,
      })),
    }));
    managerMock.getCustomRepository = jest.fn().mockImplementation(() => ({
      createQueryBuilder: jest.fn(() => ({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        delete: jest.fn(),
        execute: jest.fn(),
        getOne: jest.fn(() => bill),
      })),
      save: jest.fn(),
    }));

    const result = await service.deleteAutoGeneratedBillItems(
      profile,
      {},
      ['ref-id'],
      managerMock,
      undefined,
    );
    expect(result?.[0]).toBeFalsy();
  });

  it('deleteAutoGeneratedBillItems() should not run if references is empty', async () => {
    const result = await service.deleteAutoGeneratedBillItems(
      profile,
      {},
      [],
      managerMock,
      undefined,
    );
    expect(result).toBe(undefined);
  });

  it('deleteAutoGeneratedBill() should delete auto-generated bill', async () => {
    managerMock.createQueryBuilder = jest.fn().mockImplementation(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      returning: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      execute: jest.fn(() => ({
        raw: [
          {
            bill: 'bill',
            amount: 0,
            amount_outstanding: 0,
            discount_amount: 0,
            amount_owing: 0,
          },
        ],
        affected: 1,
      })),
    }));
    const { details, ...rest } = bill;

    mockBillRepository.createQueryBuilder = jest.fn(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      delete: jest.fn(),
      execute: jest.fn(),
      getOne: jest.fn(() => rest),
    })) as any;
    await service.deleteAutoGeneratedBillItems(
      profile,
      {},
      ['ref-id'],
      managerMock,
      undefined,
    );
    expect(managerMock.createQueryBuilder).toHaveBeenCalled();
  });

  it('excludeBillDetail() should exclude patient bill item', async () => {
    const response = await service.excludeBillDetail(
      [{ id: 'ssd', excluded: false }],
      'ddds',
    );

    expect(mockBillRepository.excludeBillDetail).toHaveBeenCalledTimes(1);
    expect(mockBillRepository.excludeBillDetail).toHaveBeenCalledWith(
      [{ id: 'ssd', excluded: false }],
      'ddds',
    );
    expect(response).toEqual(bill);
  });

  it('generateEmptyBillIfNone() should return undefined if a bill with no same service type has already been generated', async () => {
    managerMock.getCustomRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn(() => bill),
    }));
    managerMock.findOne = jest.fn(() => null);

    profile.type = UserType.OrganizationDoctor;
    profile.hospital = hospital;
    const emptyBill = await service.generateEmptyBillIfNone(
      profile,
      user.defaultProfile,
      {},
      managerMock,
      [],
    );

    expect(emptyBill).toBe(bill);
  });

  it('generateEmptyBillIfNone() should return an empty bill if no bill has been generated', async () => {
    const emptyBill = { ...bill, details: [] };
    mockBillRepository.findOne = jest.fn(() => null);
    mockBillRepository.createOrgBill = jest.fn(() => emptyBill);
    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn(() => emptyBill),
      createQueryBuilder: MockedQueryBuilder,
    }));

    profile.type = UserType.OrganizationDoctor;
    profile.hospital = hospital;
    const generatedEmptyBill = await service.generateEmptyBillIfNone(
      profile,
      user.defaultProfile,
      {},
      managerMock,
    );

    expect(generatedEmptyBill).toBe(emptyBill);
  });

  it('generateEmptyBillIfNone() should return undefined when mutator is patient', async () => {
    mockBillRepository.findOne = jest.fn(() => null);
    profile.type = UserType.Patient;
    profile.hospital = hospital;
    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn(() => null),
      createQueryBuilder: MockedQueryBuilder,
    }));

    const generatedEmptyBill = await service.generateEmptyBillIfNone(
      profile,
      user.defaultProfile,
      {},
      managerMock,
    );

    expect(generatedEmptyBill).toBe(undefined);
  });

  it('generateMultipleBill() should create bill', async () => {
    const ref = chance.guid({ version: 4 });
    const mockBill = billFactory.build();

    const mutator = new ProfileModel();
    mutator.id = chance.guid({ version: 4 });
    mutator.type = UserType.OrganizationNurse;
    mutator.hospital = hospital;

    const receiverProfile = new ProfileModel();
    receiverProfile.id = chance.guid({ version: 4 });
    receiverProfile.type = UserType.Patient;

    const generatedBill = await service.generateMultipleBill(
      receiverProfile,
      receiverProfile,
      {},
      ServiceType.Procedure,
      [
        {
          reference: ref,
          price: 1400,
          quantity: 2,
          subBills: null,
          generateBillName: false,
          billName: chance.name(),
          serviceType: chance.name(),
          serviceName: chance.name(),
        },
      ],
      managerMock,
    );

    expect(managerMock.save).not.toHaveBeenCalled();
    expect(generatedBill).toBeFalsy();

    managerMock.getCustomRepository().findOne = jest.fn(() =>
      Promise.resolve(mockBill),
    );

    managerMock.findOne = jest.fn(() => Promise.resolve(mockBill.details[0]));

    const generatedBill1 = await service.generateMultipleBill(
      mutator,
      receiverProfile,
      {},
      ServiceType.Procedure,
      [
        {
          reference: ref,
          price: 1400,
          quantity: 2,
          subBills: null,
          generateBillName: false,
          billName: chance.name(),
          serviceType: chance.name(),
          serviceName: chance.name(),
        },
      ],
      managerMock,
    );

    expect(managerMock.save).not.toHaveBeenCalled();
    expect(generatedBill1).toBeFalsy();

    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn(() => ({ ...bill, amountUnderpaid: 1000 })),
      createQueryBuilder: MockedQueryBuilder,
    }));

    managerMock.getCustomRepository().findOne = jest.fn(() =>
      Promise.resolve(mockBill),
    );

    managerMock.findOne = jest.fn(() => Promise.resolve(null));

    await service.generateMultipleBill(
      mutator,
      receiverProfile,
      {},
      ServiceType.Procedure,
      [
        {
          reference: ref,
          price: 1400,
          quantity: 2,
          subBills: null,
          generateBillName: false,
          billName: chance.name(),
          serviceType: chance.name(),
          serviceName: chance.name(),
        },
      ],
      managerMock,
    );

    expect(managerMock.save).toHaveBeenCalled();

    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn(() => ({ ...bill, amountOverpaid: 1000 })),
      createQueryBuilder: MockedQueryBuilder,
    }));

    await service.generateMultipleBill(
      mutator,
      receiverProfile,
      {},
      ServiceType.Procedure,
      [
        {
          reference: ref,
          price: 1400,
          quantity: 2,
          generateBillName: false,
          subBills: [
            {
              serviceType: '',
              subServiceType: '',
              description: '',
              quantity: 1,
              unitPrice: 500,
              pricePerUnit: 500,
            } as any,
          ],
          billName: chance.name(),
          serviceType: chance.name(),
          serviceName: chance.name(),
        },
      ],
      managerMock,
    );

    expect(managerMock.save).toHaveBeenCalled();

    await service.generateMultipleBill(
      mutator,
      receiverProfile,
      { medicationName: ['Panadol'] },
      ServiceType.Medication,
      [
        {
          reference: ref,
          price: 1400,
          quantity: 2,
          subBills: null,
          generateBillName: true,
          billName: chance.name(),
          serviceType: chance.name(),
          serviceName: chance.name(),
        },
      ],
      managerMock,
    );

    expect(managerMock.save).toHaveBeenCalled();
    expect(
      MockPaymentDepositService.makeServicePaymentFromDeposit,
    ).not.toHaveBeenCalled();
  });

  it('updateMultipleBill() should update bill', async () => {
    const mockBill = billFactory.build();
    const defaultServiceDetail = {
      priceId: '',
      type: '',
      name: '',
      quantity: '1',
      pricePerUnit: 0,
      itemId: '',
      ref: '',
      subBills: null,
      description: '',
    };

    const mutator = new ProfileModel();
    mutator.id = chance.guid({ version: 4 });
    mutator.type = UserType.OrganizationNurse;
    mutator.hospital = hospital;

    const receiverProfile = new ProfileModel();
    receiverProfile.id = chance.guid({ version: 4 });
    receiverProfile.type = UserType.Patient;

    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn(() => ({ ...bill, amountOverpaid: 1000 })),
      createQueryBuilder: MockedQueryBuilder,
    }));

    const updateddBill = await service.updateMultipleBill(
      receiverProfile,
      receiverProfile,
      {},
      ServiceType.Investigation,
      chance.guid({ version: 4 }),
      [defaultServiceDetail],
      [],
      managerMock,
    );

    expect(updateddBill).toBeFalsy();
    expect(managerMock.save).not.toHaveBeenCalled();

    managerMock.getCustomRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn(() => mockBill),
    }));
    managerMock.findOne = jest.fn(() => Promise.resolve(mockBill.details[0]));

    await service.updateMultipleBill(
      mutator,
      receiverProfile,
      {},
      ServiceType.Investigation,
      chance.guid({ version: 4 }),
      [defaultServiceDetail],
      [],
      managerMock,
    );

    expect(managerMock.save).toHaveBeenCalled();

    await service.updateMultipleBill(
      mutator,
      receiverProfile,
      {},
      ServiceType.Investigation,
      chance.guid({ version: 4 }),
      [
        {
          ...defaultServiceDetail,
          subBills: [
            {
              serviceType: '',
              subServiceType: '',
              description: '',
              quantity: 1,
              unitPrice: 500,
              pricePerUnit: 500,
            } as any,
          ],
        },
      ],
      [],
      managerMock,
    );

    expect(managerMock.save).toHaveBeenCalled();

    managerMock.findOne = jest.fn(() => Promise.resolve(null));

    await service.updateMultipleBill(
      mutator,
      receiverProfile,
      {},
      ServiceType.Investigation,
      chance.guid({ version: 4 }),
      [defaultServiceDetail],
      [],
      managerMock,
    );

    expect(managerMock.save).toHaveBeenCalled();

    await service.updateMultipleBill(
      mutator,
      receiverProfile,
      {},
      ServiceType.Investigation,
      chance.guid({ version: 4 }),
      [
        {
          ...defaultServiceDetail,
          subBills: [
            {
              serviceType: '',
              subServiceType: '',
              description: '',
              quantity: 1,
              unitPrice: 500,
              pricePerUnit: 500,
            } as any,
          ],
        },
      ],
      [],
      managerMock,
    );

    expect(managerMock.save).toHaveBeenCalled();

    await service.updateMultipleBill(
      mutator,
      receiverProfile,
      {},
      ServiceType.Investigation,
      chance.guid({ version: 4 }),
      [
        {
          ...defaultServiceDetail,
          ref: mockBill.details[0].reference,
          subBills: [
            {
              serviceType: '',
              subServiceType: '',
              description: '',
              quantity: 1,
              unitPrice: 500,
              pricePerUnit: 500,
            } as any,
          ],
        },
      ],
      [],
      managerMock,
    );

    expect(managerMock.save).toHaveBeenCalled();

    await service.updateMultipleBill(
      mutator,
      receiverProfile,
      {},
      ServiceType.Investigation,
      chance.guid({ version: 4 }),
      [
        {
          ...defaultServiceDetail,
          ref: chance.guid(),
          subBills: [
            {
              serviceType: '',
              subServiceType: '',
              description: '',
              quantity: 1,
              unitPrice: 500,
              pricePerUnit: 500,
            } as any,
          ],
        },
      ],
      [],
      managerMock,
    );

    expect(managerMock.save).toHaveBeenCalled();
    expect(
      MockPaymentDepositService.makeServicePaymentFromDeposit,
    ).not.toHaveBeenCalled();

    const mockBillToUse = { ...mockBill, amountUnderpaid: 1000 };
    mockBillToUse.details[0].reference = 'the-reference';

    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn(() => mockBillToUse),
      createQueryBuilder: MockedQueryBuilder,
    }));

    await service.updateMultipleBill(
      mutator,
      receiverProfile,
      {},
      ServiceType.Investigation,
      chance.guid({ version: 4 }),
      [{ ...defaultServiceDetail, ref: 'the-reference' }],
      [],
      managerMock,
    );
  });

  it('updateBillDetail() should update bill details', async () => {
    jest.spyOn(mockBillRepository, 'createQueryBuilder').mockImplementation(
      () =>
        ({
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getOne: jest.fn(() => ({ ...mockBill, details: [] })),
        } as any),
    );
    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn(() => bill),
      createQueryBuilder: MockedQueryBuilder,
    }));
    const mockBill = billFactory.build();
    const mockBillDetails = billDetailsFactory.buildList(2);
    const mockBillDetail = billDetailsFactory.build();
    const billId = chance.guid({ version: 4 });
    mockBillRepository.save = jest.fn();
    const mutator = new ProfileModel();
    mutator.id = chance.guid({ version: 4 });
    mutator.type = UserType.OrganizationNurse;
    mutator.hospital = hospital;

    const receiverProfile = new ProfileModel();
    receiverProfile.id = chance.guid({ version: 4 });
    receiverProfile.type = UserType.Patient;

    const result = await service.updateBillDetail(
      managerMock,
      mutator,
      receiverProfile,
      ServiceType.Antenatal,
      [],
    );
    expect(result).toBeFalsy();

    mockBillRepository.findOne = jest.fn(() => mockBill);

    managerMock.createQueryBuilder = jest.fn().mockImplementation(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      getOne: jest.fn(() => mockBill),
      getMany: jest.fn(() => [
        ...mockBill.details.map((detail) => ({
          ...detail,
          billId,
        })),
        { ...mockBillDetail, billId: chance.guid({ version: 4 }) },
      ]),
    }));

    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn(() => ({ ...bill, amountUnderpaid: 1000 })),
      createQueryBuilder: MockedQueryBuilder,
    }));

    managerMock.findOne = jest.fn(() => Promise.resolve(mockBill.details[0]));

    await service.updateBillDetail(
      managerMock,
      mutator,
      receiverProfile,
      ServiceType.Antenatal,
      [...mockBill.details, ...mockBillDetails].map((detail) => ({
        ...detail,
        ref: detail.reference,
      })),
    );

    expect(managerMock.save).toHaveBeenCalled();
    expect(
      MockPaymentDepositService.makeServicePaymentFromDeposit,
    ).not.toHaveBeenCalled();

    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn(() => ({ ...bill, amountOverpaid: 1000 })),
      createQueryBuilder: MockedQueryBuilder,
    }));

    managerMock.findOne = jest.fn(() => Promise.resolve(mockBill.details[0]));

    await service.updateBillDetail(
      managerMock,
      mutator,
      receiverProfile,
      ServiceType.Antenatal,
      [...mockBill.details, ...mockBillDetails].map((detail) => ({
        ...detail,
        ref: detail.reference,
      })),
    );

    expect(managerMock.save).toHaveBeenCalled();
    expect(
      MockPaymentDepositService.makeServicePaymentFromDeposit,
    ).not.toHaveBeenCalled();
  });

  it('changeBillDetailParentBill() should change parent bill', async () => {
    const mutator = new ProfileModel();
    mutator.id = chance.guid({ version: 4 });
    mutator.type = UserType.OrganizationNurse;
    mutator.hospital = hospital;

    const newParentBillId = chance.guid();

    mockBillRepository.findOneOrFail = jest.fn(() =>
      Promise.resolve(billFactory.build()),
    );

    await service.changeBillDetailParentBill(mutator, {
      id: chance.guid(),
      newParentBillId,
    });

    expect(mockBillDetailsRepository.updateParentBill).toHaveBeenCalled();

    mockBillRepository.findOneOrFail = jest.fn(() => Promise.reject());

    await expect(
      service.changeBillDetailParentBill(mutator, {
        id: chance.guid(),
        newParentBillId,
      }),
    ).rejects.toThrow(`Bill With ID ${newParentBillId} Not Found`);
  });

  it('saveWalkInHospitalBill(): should call createWalkInBill repository method', async () => {
    const mutator = profileFactory.build();
    const input = billFactory.build({
      profileId: null,
      patientInformation: {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '**********',
        clinifyId: '',
      },
    });

    await service.saveWalkInHospitalBill(mutator, input);

    expect(mockBillRepository.createWalkInBill).toHaveBeenCalledWith(
      mutator,
      input,
    );
  });

  it('admissionSeverity(): get servirity of patient from admission record', async () => {
    await service.getAdmissionSeverity('admission-id');

    expect(managerMock.findOne).toHaveBeenLastCalledWith(AdmissionModel, {
      select: ['severeness'],
      where: { id: 'admission-id' },
    });
  });

  it('completeBillPayment(): should throw error when bill is not found', async () => {
    const mutator = profileFactory.build();
    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOneOrFail: jest.fn().mockRejectedValue(''),
      save: jest.fn(),
    }));

    await expect(
      service.completeBillPayment(mutator, 'bill-id'),
    ).rejects.toThrow('Bill Not Found');
  });

  it('completeBillPayment(): should throw error when non authorized user mutates bill', async () => {
    const mutator = profileFactory.build({
      hospital: { id: 'hospital-id' },
    });
    const bill = billFactory.build({
      senderHospital: { id: 'hospital-id-2' },
    });
    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOneOrFail: jest.fn().mockResolvedValue(bill),
      save: jest.fn(),
    }));

    await expect(
      service.completeBillPayment(mutator, 'bill-id'),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('completeBillPayment(): should complete bill payment', async () => {
    const mutator = profileFactory.build({
      hospital: { id: 'hospital-id' },
    });

    let bill = billFactory.build({
      senderHospital: { id: 'hospital-id-2' },
      createdBy: mutator,
      details: billDetailsFactory.buildList(2, {
        splitReference: 'true',
        serviceType: 'Service Type',
        subServiceType: 'Sub Service Type',
      }),
    });
    bill.details = [
      ...bill.details,
      billDetailsFactory.build({ excluded: true }),
    ];
    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOneOrFail: jest.fn().mockResolvedValue(bill),
      save: jest.fn(),
    }));

    await service.completeBillPayment(mutator, 'bill-id');

    bill.details = [];

    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOneOrFail: jest.fn().mockResolvedValue(bill),
      save: jest.fn(),
    }));

    await service.completeBillPayment(mutator, 'bill-id');

    const billSavedAction = jest.fn();

    bill = billFactory.build({
      senderHospital: { id: 'hospital-id-2' },
      createdBy: mutator,
      details: billDetailsFactory.buildList(2, {
        subBills: billDetailsFactory.buildList(1),
      }),
    });
    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOneOrFail: jest.fn().mockResolvedValue(bill),
      save: billSavedAction,
    }));

    await service.completeBillPayment(mutator, 'bill-id');

    expect(billSavedAction).toHaveBeenCalledWith(
      expect.objectContaining({ billStatus: BillStatus.Paid }),
    );
  });

  it('addVirtualServicesPayment(): should add virtual services payment', async () => {
    const mutator = profileFactory.build({
      hospital: { id: 'hospital-id', name: 'Test Hospital' },
    });
    const billId = 'bill-id';
    const input: VirtualServicesPaymentInput = {
      paymentMethod: 'WEMA Bank Transfer',
      amountDue: 1000,
      amountPaid: 0,
      paymentStatus: 'Pending',
      commissionFeeAmount: 5,
      commissionPayer: CommissionPayer.Patient,
    };

    const bill = billFactory.build({
      id: billId,
      details: billDetailsFactory.buildList(2, {
        paymentType: 'WEMA Bank Transfer',
        amountDue: 500,
      }),
    });

    managerMock.withRepository = jest.fn().mockImplementation(() => ({
      findOne: jest.fn().mockResolvedValue(bill),
    }));
    managerMock.save = jest.fn().mockImplementation((entity) => entity);

    const result = await service.addVirtualServicesPayment(
      mutator,
      billId,
      input,
      managerMock,
    );

    expect(result).toBeDefined();
    expect(result.billId).toEqual(billId);
    expect(result.paymentMethod).toEqual(input.paymentMethod);
    expect(result.amountDue).toEqual(input.amountDue);
    expect(result.amountPaid).toEqual(input.amountPaid);
    expect(result.paymentStatus).toEqual(input.paymentStatus);
    expect(result.commissionFeeAmount).toEqual(input.commissionFeeAmount);
    expect(result.commissionPayer).toEqual(input.commissionPayer);
    expect(mockBankService.generateVirtualAccountNumber).toHaveBeenCalledWith(
      expect.objectContaining({
        bankCode: VirtualAccountProvider.WEMA,
        virtualAccountType: VirtualAccountType.Temporary,
        vaTransactionType: VirtualAccountTransactionType.PayBill,
      }),
      mutator.hospital.name,
      managerMock,
      undefined,
      billId,
    );
  });

  it('updateBillInformationFromPaymentNotification(): should update bill information from payment notification', async () => {
    const amountPaid = 1000;
    const billId = 'bill-id';
    const paymentType = 'WEMA Bank Transfer';

    const bill = billFactory.build({
      id: billId,
      amountPaid: 0,
      amountOutstanding: 1000,
      details: billDetailsFactory.buildList(2, {
        paymentType,
        amountDue: 500,
        amountPaid: 0,
        amountOutstanding: 500,
      }),
      virtualServicesPayment: {
        amountDue: 1000,
        amountPaid: 0,
        paymentStatus: 'Pending',
      },
    });

    const billRepository = {
      findOne: jest.fn().mockResolvedValue({
        ...bill,
        virtualServicesPayment: {
          amountDue: 1000,
          amountPaid: 0,
          paymentStatus: 'Pending',
        },
      }),
      save: jest.fn().mockImplementation((entity) => entity),
    };

    managerMock.withRepository = jest.fn().mockReturnValue(billRepository);
    managerMock.save = jest.fn().mockImplementation((entity) => entity);

    const result = await service.updateBillInformationFromPaymentNotification(
      amountPaid,
      billId,
      paymentType,
      managerMock,
    );

    expect(result).toBeDefined();
    expect(result.amountPaid).toEqual(amountPaid);
    expect(result.amountOutstanding).toEqual(0);
    expect(result.details[0].amountPaid).toEqual(500);
    expect(result.details[0].amountOutstanding).toEqual(0);
    expect(result.details[1].amountPaid).toEqual(500);
    expect(result.details[1].amountOutstanding).toEqual(0);
    expect(pubSubMock.publish).toHaveBeenCalledWith('OrgBillUpdated', {
      OrgBillUpdated: result,
    });
  });

  it('updateVirtualServicesPayment(): should update virtual services payment', async () => {
    const mutator = profileFactory.build();
    const id = 'virtual-services-payment-id';
    const input: VirtualServicesPaymentInput = {
      paymentMethod: 'WEMA Bank Transfer',
      amountDue: 1000,
      amountPaid: 500,
      paymentStatus: 'Partially Paid',
      commissionFeeAmount: 5,
      commissionPayer: CommissionPayer.Patient,
    };

    const virtualServicesPayment = new VirtualServicesPaymentModel({
      id,
      paymentMethod: 'WEMA Bank Transfer',
      amountDue: 1000,
      amountPaid: 0,
      paymentStatus: 'Pending',
      commissionFeeAmount: 5,
      commissionPayer: CommissionPayer.Patient,
      virtualBankAccount: {
        isActive: true,
      } as any,
    });

    managerMock.findOneOrFail = jest
      .fn()
      .mockResolvedValue(virtualServicesPayment);
    managerMock.save = jest.fn().mockImplementation((entity) => entity);

    const result = await service.updateVirtualServicesPayment(
      mutator,
      id,
      input,
    );

    expect(result).toBeDefined();
    expect(result.paymentMethod).toEqual(input.paymentMethod);
    expect(result.amountDue).toEqual(input.amountDue);
    expect(result.amountPaid).toEqual(input.amountPaid);
    expect(result.paymentStatus).toEqual(input.paymentStatus);
    expect(result.commissionFeeAmount).toEqual(input.commissionFeeAmount);
    expect(result.commissionPayer).toEqual(input.commissionPayer);
    expect(result.updatedBy).toEqual(mutator);
    expect(result.lastModifierId).toEqual(mutator.id);
  });

  it('updateVirtualServicesPayment(): should throw error if virtual account is expired', async () => {
    const mutator = profileFactory.build();
    const id = 'virtual-services-payment-id';
    const input: VirtualServicesPaymentInput = {
      paymentMethod: 'WEMA Bank Transfer',
      amountDue: 1000,
      amountPaid: 500,
      paymentStatus: 'Partially Paid',
      commissionFeeAmount: 5,
      commissionPayer: CommissionPayer.Patient,
    };

    const virtualServicesPayment = new VirtualServicesPaymentModel({
      id,
      paymentMethod: 'WEMA Bank Transfer',
      amountDue: 1000,
      amountPaid: 0,
      paymentStatus: 'Pending',
      commissionFeeAmount: 5,
      commissionPayer: CommissionPayer.Patient,
      virtualBankAccount: {
        isActive: false,
      } as any,
    });

    managerMock.findOneOrFail = jest
      .fn()
      .mockResolvedValue(virtualServicesPayment);

    await expect(
      service.updateVirtualServicesPayment(mutator, id, input),
    ).rejects.toThrow('Virtual Account Is Expired');
  });
});
