import {MigrationInterface, QueryRunner} from "typeorm";

export class UpdateProfileIdRelation1661954932850 implements MigrationInterface {
    name = 'UpdateProfileIdRelation1661954932850'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // ADMISSION
        await queryRunner.query(`ALTER TABLE "admissions" DROP CONSTRAINT "FK_48677d08e23d81ae6f5023528fa"`);
        await queryRunner.query(`CREATE INDEX "IDX_48677d08e23d81ae6f5023528f" ON "admissions" ("profile") `);
        await queryRunner.query(`ALTER TABLE "admissions" ADD CONSTRAINT "FK_48677d08e23d81ae6f5023528fa" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // ALLERGIES
        await queryRunner.query(`ALTER TABLE "allergies" DROP CONSTRAINT "FK_58f7a9609aa76b6ea0b581b0280"`);
        await queryRunner.query(`CREATE INDEX "IDX_58f7a9609aa76b6ea0b581b028" ON "allergies" ("profile") `);
        await queryRunner.query(`ALTER TABLE "allergies" ADD CONSTRAINT "FK_58f7a9609aa76b6ea0b581b0280" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // PARTOGRAPH
        await queryRunner.query(`ALTER TABLE "partograph" DROP CONSTRAINT "FK_a8703d7910ebaeb07b85856ab0c"`);
        await queryRunner.query(`CREATE INDEX "IDX_a8703d7910ebaeb07b85856ab0" ON "partograph" ("profile") `);
        await queryRunner.query(`ALTER TABLE "partograph" ADD CONSTRAINT "FK_a8703d7910ebaeb07b85856ab0c" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // ANTENATAL
        await queryRunner.query(`ALTER TABLE "antenatals" DROP CONSTRAINT "FK_31a75238541428d4434091a46d0"`);
        await queryRunner.query(`CREATE INDEX "IDX_31a75238541428d4434091a46d" ON "antenatals" ("profile") `);
        await queryRunner.query(`ALTER TABLE "antenatals" ADD CONSTRAINT "FK_31a75238541428d4434091a46d0" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // APPOINTMENT
        await queryRunner.query(`ALTER TABLE "organisation_appointments" DROP CONSTRAINT "FK_78480a42f74668cc89991f650d0"`);
        await queryRunner.query(`CREATE INDEX "IDX_78480a42f74668cc89991f650d" ON "organisation_appointments" ("profile") `);
        await queryRunner.query(`ALTER TABLE "organisation_appointments" ADD CONSTRAINT "FK_78480a42f74668cc89991f650d0" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // CARD
        await queryRunner.query(`ALTER TABLE "cards" DROP CONSTRAINT "FK_3958160cdc723507dcabef9787e"`);
        await queryRunner.query(`CREATE INDEX "IDX_3958160cdc723507dcabef9787" ON "cards" ("profile") `);
        await queryRunner.query(`ALTER TABLE "cards" ADD CONSTRAINT "FK_3958160cdc723507dcabef9787e" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // CONSULTATION
        await queryRunner.query(`ALTER TABLE "consultations" DROP CONSTRAINT "FK_d94c63fd1745e496f509b2047af"`);
        await queryRunner.query(`CREATE INDEX "IDX_d94c63fd1745e496f509b2047a" ON "consultations" ("profile") `);
        await queryRunner.query(`ALTER TABLE "consultations" ADD CONSTRAINT "FK_d94c63fd1745e496f509b2047af" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // HMO CLAIMS ****
        await queryRunner.query(`ALTER TABLE "hmo_claims" DROP CONSTRAINT "FK_53a58dcc611b38b620d93342742"`);
        await queryRunner.query(`CREATE INDEX "IDX_53a58dcc611b38b620d9334274" ON "hmo_claims" ("profile_id") `);
        await queryRunner.query(`ALTER TABLE "hmo_claims" ADD CONSTRAINT "FK_53a58dcc611b38b620d93342742" FOREIGN KEY ("profile_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // HMO PROFILES ****
        await queryRunner.query(`ALTER TABLE "hmo_profiles" DROP CONSTRAINT "FK_208b0c02ca91e2cd1e246436e11"`);
        await queryRunner.query(`CREATE INDEX "IDX_208b0c02ca91e2cd1e246436e1" ON "hmo_profiles" ("profile_id") `);
        await queryRunner.query(`ALTER TABLE "hmo_profiles" ADD CONSTRAINT "FK_208b0c02ca91e2cd1e246436e11" FOREIGN KEY ("profile_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // IMMUNIZATION
        await queryRunner.query(`ALTER TABLE "immunizations" DROP CONSTRAINT "FK_4ee1a43a5b1d7d1299f2d5d617f"`);
        await queryRunner.query(`CREATE INDEX "IDX_4ee1a43a5b1d7d1299f2d5d617" ON "immunizations" ("profile") `);
        await queryRunner.query(`ALTER TABLE "immunizations" ADD CONSTRAINT "FK_4ee1a43a5b1d7d1299f2d5d617f" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // INVESTIGATION
        await queryRunner.query(`ALTER TABLE "investigations" DROP CONSTRAINT "FK_d8057a9ae5d3771a25513ade706"`);
        await queryRunner.query(`CREATE INDEX "IDX_d8057a9ae5d3771a25513ade70" ON "investigations" ("profile") `);
        await queryRunner.query(`ALTER TABLE "investigations" ADD CONSTRAINT "FK_d8057a9ae5d3771a25513ade706" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MEDICATION
        await queryRunner.query(`ALTER TABLE "medications" DROP CONSTRAINT "FK_d9eb5a949f0d1e384131a424565"`);
        await queryRunner.query(`CREATE INDEX "IDX_d9eb5a949f0d1e384131a42456" ON "medications" ("profile") `);
        await queryRunner.query(`ALTER TABLE "medications" ADD CONSTRAINT "FK_d9eb5a949f0d1e384131a424565" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // PRE AUTHORIZATION ****
        await queryRunner.query(`ALTER TABLE "pre_authorizations" DROP CONSTRAINT "FK_60fdacc39101912dba254b7ae17"`);
        await queryRunner.query(`CREATE INDEX "IDX_60fdacc39101912dba254b7ae1" ON "pre_authorizations" ("profile_id") `);
        await queryRunner.query(`ALTER TABLE "pre_authorizations" ADD CONSTRAINT "FK_60fdacc39101912dba254b7ae17" FOREIGN KEY ("profile_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        
        // CONSUMABLES
        await queryRunner.query(`ALTER TABLE "consumables" DROP CONSTRAINT "FK_1a9ba5bbdbf669c0fdba9e28f5f"`);
        await queryRunner.query(`CREATE INDEX "IDX_1a9ba5bbdbf669c0fdba9e28f5" ON "consumables" ("profile") `);
        await queryRunner.query(`ALTER TABLE "consumables" ADD CONSTRAINT "FK_1a9ba5bbdbf669c0fdba9e28f5f" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);


        // PROCEDURE
        await queryRunner.query(`ALTER TABLE "surgeries" DROP CONSTRAINT "FK_987b5fb5345e88a3328cf642419"`);
        await queryRunner.query(`CREATE INDEX "IDX_987b5fb5345e88a3328cf64241" ON "surgeries" ("profile") `);
        await queryRunner.query(`ALTER TABLE "surgeries" ADD CONSTRAINT "FK_987b5fb5345e88a3328cf642419" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // PRE OPERATION CHECKLIST
        await queryRunner.query(`ALTER TABLE "pre_operation_checklist" DROP CONSTRAINT "FK_25a42fbfa39df8d2075e12a813f"`);
        await queryRunner.query(`CREATE INDEX "IDX_25a42fbfa39df8d2075e12a813" ON "pre_operation_checklist" ("profile") `);
        await queryRunner.query(`ALTER TABLE "pre_operation_checklist" ADD CONSTRAINT "FK_25a42fbfa39df8d2075e12a813f" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);


        // MY HEALTH - Dependencies
        await queryRunner.query(`ALTER TABLE "dependents" DROP CONSTRAINT "FK_8a01d09120b50f5497588861594"`);
        await queryRunner.query(`CREATE INDEX "IDX_8a01d09120b50f549758886159" ON "dependents" ("profile") `);
        await queryRunner.query(`ALTER TABLE "dependents" ADD CONSTRAINT "FK_8a01d09120b50f5497588861594" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Disability
        await queryRunner.query(`ALTER TABLE "disabilities" DROP CONSTRAINT "FK_aa45a88551278eaddb7a8262884"`);
        await queryRunner.query(`CREATE INDEX "IDX_aa45a88551278eaddb7a826288" ON "disabilities" ("profile") `);
        await queryRunner.query(`ALTER TABLE "disabilities" ADD CONSTRAINT "FK_aa45a88551278eaddb7a8262884" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Family History
        await queryRunner.query(`ALTER TABLE "family_histories" DROP CONSTRAINT "FK_ec713e64baa29cb1cc742ce4e5d"`);
        await queryRunner.query(`CREATE INDEX "IDX_ec713e64baa29cb1cc742ce4e5" ON "family_histories" ("profile") `);
        await queryRunner.query(`ALTER TABLE "family_histories" ADD CONSTRAINT "FK_ec713e64baa29cb1cc742ce4e5d" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Gynecologic History
        await queryRunner.query(`ALTER TABLE "geynecologic_histories" DROP CONSTRAINT "FK_7cfe0923aac067ba2e3095bf27e"`);
        await queryRunner.query(`CREATE INDEX "IDX_7cfe0923aac067ba2e3095bf27" ON "geynecologic_histories" ("profile") `);
        await queryRunner.query(`ALTER TABLE "geynecologic_histories" ADD CONSTRAINT "FK_7cfe0923aac067ba2e3095bf27e" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Habit
        await queryRunner.query(`ALTER TABLE "habits" DROP CONSTRAINT "FK_37e754ef8e40b8418aef6174d8b"`);
        await queryRunner.query(`CREATE INDEX "IDX_37e754ef8e40b8418aef6174d8" ON "habits" ("profile") `);
        await queryRunner.query(`ALTER TABLE "habits" ADD CONSTRAINT "FK_37e754ef8e40b8418aef6174d8b" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Next Of Kin
        await queryRunner.query(`ALTER TABLE "next_of_kin" DROP CONSTRAINT "FK_aae80bc271eb191298c26bda36d"`);
        await queryRunner.query(`CREATE INDEX "IDX_aae80bc271eb191298c26bda36" ON "next_of_kin" ("profile") `);
        await queryRunner.query(`ALTER TABLE "next_of_kin" ADD CONSTRAINT "FK_aae80bc271eb191298c26bda36d" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Obstetric History
        await queryRunner.query(`ALTER TABLE "obstetric_histories" DROP CONSTRAINT "FK_fc18818f7dcc2643b836117607a"`);
        await queryRunner.query(`CREATE INDEX "IDX_fc18818f7dcc2643b836117607" ON "obstetric_histories" ("profile") `);
        await queryRunner.query(`ALTER TABLE "obstetric_histories" ADD CONSTRAINT "FK_fc18818f7dcc2643b836117607a" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Oncology History
        await queryRunner.query(`ALTER TABLE "oncology_histories" DROP CONSTRAINT "FK_d0c00b97364cb3bdf5a3f76f7e1"`);
        await queryRunner.query(`CREATE INDEX "IDX_d0c00b97364cb3bdf5a3f76f7e" ON "oncology_histories" ("profile") `);
        await queryRunner.query(`ALTER TABLE "oncology_histories" ADD CONSTRAINT "FK_d0c00b97364cb3bdf5a3f76f7e1" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Past Encounter
        await queryRunner.query(`ALTER TABLE "past_encounters" DROP CONSTRAINT "FK_e80f83614efe26e1016b384c4ee"`);
        await queryRunner.query(`CREATE INDEX "IDX_e80f83614efe26e1016b384c4e" ON "past_encounters" ("profile") `);
        await queryRunner.query(`ALTER TABLE "past_encounters" ADD CONSTRAINT "FK_e80f83614efe26e1016b384c4ee" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Past Surgery
        await queryRunner.query(`ALTER TABLE "past_surgery" DROP CONSTRAINT "FK_d08f7fcab9b81fc38fb0a63b1f7"`);
        await queryRunner.query(`CREATE INDEX "IDX_d08f7fcab9b81fc38fb0a63b1f" ON "past_surgery" ("profile") `);
        await queryRunner.query(`ALTER TABLE "past_surgery" ADD CONSTRAINT "FK_d08f7fcab9b81fc38fb0a63b1f7" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Physical Activity
        await queryRunner.query(`ALTER TABLE "physical_activity" DROP CONSTRAINT "FK_1d60c98a3886ae3d42f0ce99fb4"`);
        await queryRunner.query(`CREATE INDEX "IDX_1d60c98a3886ae3d42f0ce99fb" ON "physical_activity" ("profile") `);
        await queryRunner.query(`ALTER TABLE "physical_activity" ADD CONSTRAINT "FK_1d60c98a3886ae3d42f0ce99fb4" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Pre Existing Condition
        await queryRunner.query(`ALTER TABLE "pre_existing_conditions" DROP CONSTRAINT "FK_0211fe5aa03a0639f1739c5d458"`);
        await queryRunner.query(`CREATE INDEX "IDX_0211fe5aa03a0639f1739c5d45" ON "pre_existing_conditions" ("profile") `);
        await queryRunner.query(`ALTER TABLE "pre_existing_conditions" ADD CONSTRAINT "FK_0211fe5aa03a0639f1739c5d458" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);


        // VITALS
        await queryRunner.query(`ALTER TABLE "vitals" DROP CONSTRAINT "FK_f0a2dbdb3757326fc0e185d8133"`);
        await queryRunner.query(`CREATE INDEX "IDX_f0a2dbdb3757326fc0e185d813" ON "vitals" ("profile") `);
        await queryRunner.query(`ALTER TABLE "vitals" ADD CONSTRAINT "FK_f0a2dbdb3757326fc0e185d8133" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Anthropometry
        await queryRunner.query(`ALTER TABLE "anthropometry" DROP CONSTRAINT "FK_1057cdffa9c99fa29baa247e15b"`);
        await queryRunner.query(`CREATE INDEX "IDX_1057cdffa9c99fa29baa247e15" ON "anthropometry" ("profile") `);
        await queryRunner.query(`ALTER TABLE "anthropometry" ADD CONSTRAINT "FK_1057cdffa9c99fa29baa247e15b" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Blood Glucose
        await queryRunner.query(`ALTER TABLE "blood_glucose" DROP CONSTRAINT "FK_9505867bb285f81ae3bc52fccc6"`);
        await queryRunner.query(`CREATE INDEX "IDX_9505867bb285f81ae3bc52fccc" ON "blood_glucose" ("profile") `);
        await queryRunner.query(`ALTER TABLE "blood_glucose" ADD CONSTRAINT "FK_9505867bb285f81ae3bc52fccc6" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Blood Pressure
        await queryRunner.query(`ALTER TABLE "blood_pressure" DROP CONSTRAINT "FK_7abcb48f9239dda96ca591c048e"`);
        await queryRunner.query(`CREATE INDEX "IDX_7abcb48f9239dda96ca591c048" ON "blood_pressure" ("profile") `);
        await queryRunner.query(`ALTER TABLE "blood_pressure" ADD CONSTRAINT "FK_7abcb48f9239dda96ca591c048e" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Pain
        await queryRunner.query(`ALTER TABLE "pain" DROP CONSTRAINT "FK_21a30b7f2d05867a5b8a0f3d020"`);
        await queryRunner.query(`CREATE INDEX "IDX_21a30b7f2d05867a5b8a0f3d02" ON "pain" ("profile") `);
        await queryRunner.query(`ALTER TABLE "pain" ADD CONSTRAINT "FK_21a30b7f2d05867a5b8a0f3d020" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Pulse Rate
        await queryRunner.query(`ALTER TABLE "pulse_rate" DROP CONSTRAINT "FK_46f44f5b5d9a68b988a8e982141"`);
        await queryRunner.query(`CREATE INDEX "IDX_46f44f5b5d9a68b988a8e98214" ON "pulse_rate" ("profile") `);
        await queryRunner.query(`ALTER TABLE "pulse_rate" ADD CONSTRAINT "FK_46f44f5b5d9a68b988a8e982141" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Respiratory Rate
        await queryRunner.query(`ALTER TABLE "respiratory_rate" DROP CONSTRAINT "FK_989aa1eea6c3ac573c9b296edcb"`);
        await queryRunner.query(`CREATE INDEX "IDX_989aa1eea6c3ac573c9b296edc" ON "respiratory_rate" ("profile") `);
        await queryRunner.query(`ALTER TABLE "respiratory_rate" ADD CONSTRAINT "FK_989aa1eea6c3ac573c9b296edcb" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Temperature
        await queryRunner.query(`ALTER TABLE "temperature" DROP CONSTRAINT "FK_d02ad2e06a22decca7ca324375d"`);
        await queryRunner.query(`CREATE INDEX "IDX_d02ad2e06a22decca7ca324375" ON "temperature" ("profile") `);
        await queryRunner.query(`ALTER TABLE "temperature" ADD CONSTRAINT "FK_d02ad2e06a22decca7ca324375d" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Urine Dipstick
        await queryRunner.query(`ALTER TABLE "urine_dipstick" DROP CONSTRAINT "FK_519999128e0bb572a9a931d35ba"`);
        await queryRunner.query(`CREATE INDEX "IDX_519999128e0bb572a9a931d35b" ON "urine_dipstick" ("profile") `);
        await queryRunner.query(`ALTER TABLE "urine_dipstick" ADD CONSTRAINT "FK_519999128e0bb572a9a931d35ba" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Visual Avuity
        await queryRunner.query(`ALTER TABLE "visual_acuity" DROP CONSTRAINT "FK_448d02613b7e909c809051a74f1"`);
        await queryRunner.query(`CREATE INDEX "IDX_448d02613b7e909c809051a74f" ON "visual_acuity" ("profile") `);
        await queryRunner.query(`ALTER TABLE "visual_acuity" ADD CONSTRAINT "FK_448d02613b7e909c809051a74f1" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);


        // WALLET
        await queryRunner.query(`ALTER TABLE "wallets" DROP CONSTRAINT "FK_eb8d10b55d9ccf1a445992e21b1"`);
        await queryRunner.query(`CREATE INDEX "IDX_eb8d10b55d9ccf1a445992e21b" ON "wallets" ("profile") `);
        await queryRunner.query(`ALTER TABLE "wallets" ADD CONSTRAINT "FK_eb8d10b55d9ccf1a445992e21b1" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // WALLET
        await queryRunner.query(`ALTER TABLE "wallets" DROP CONSTRAINT "FK_eb8d10b55d9ccf1a445992e21b1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_eb8d10b55d9ccf1a445992e21b"`);
        await queryRunner.query(`ALTER TABLE "wallets" ADD CONSTRAINT "FK_eb8d10b55d9ccf1a445992e21b1" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);


        // VITALS - Visual Avuity
        await queryRunner.query(`ALTER TABLE "visual_acuity" DROP CONSTRAINT "FK_448d02613b7e909c809051a74f1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_448d02613b7e909c809051a74f"`);
        await queryRunner.query(`ALTER TABLE "visual_acuity" ADD CONSTRAINT "FK_448d02613b7e909c809051a74f1" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Urine Dipstick
        await queryRunner.query(`ALTER TABLE "urine_dipstick" DROP CONSTRAINT "FK_519999128e0bb572a9a931d35ba"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_519999128e0bb572a9a931d35b"`);
        await queryRunner.query(`ALTER TABLE "urine_dipstick" ADD CONSTRAINT "FK_519999128e0bb572a9a931d35ba" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Temperature
        await queryRunner.query(`ALTER TABLE "temperature" DROP CONSTRAINT "FK_d02ad2e06a22decca7ca324375d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d02ad2e06a22decca7ca324375"`);
        await queryRunner.query(`ALTER TABLE "temperature" ADD CONSTRAINT "FK_d02ad2e06a22decca7ca324375d" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Respiratory Rate
        await queryRunner.query(`ALTER TABLE "respiratory_rate" DROP CONSTRAINT "FK_989aa1eea6c3ac573c9b296edcb"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_989aa1eea6c3ac573c9b296edc"`);
        await queryRunner.query(`ALTER TABLE "respiratory_rate" ADD CONSTRAINT "FK_989aa1eea6c3ac573c9b296edcb" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Pulse Rate
        await queryRunner.query(`ALTER TABLE "pulse_rate" DROP CONSTRAINT "FK_46f44f5b5d9a68b988a8e982141"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_46f44f5b5d9a68b988a8e98214"`);
        await queryRunner.query(`ALTER TABLE "pulse_rate" ADD CONSTRAINT "FK_46f44f5b5d9a68b988a8e982141" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Pain
        await queryRunner.query(`ALTER TABLE "pain" DROP CONSTRAINT "FK_21a30b7f2d05867a5b8a0f3d020"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_21a30b7f2d05867a5b8a0f3d02"`);
        await queryRunner.query(`ALTER TABLE "pain" ADD CONSTRAINT "FK_21a30b7f2d05867a5b8a0f3d020" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Blood Pressure
        await queryRunner.query(`ALTER TABLE "blood_pressure" DROP CONSTRAINT "FK_7abcb48f9239dda96ca591c048e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7abcb48f9239dda96ca591c048"`);
        await queryRunner.query(`ALTER TABLE "blood_pressure" ADD CONSTRAINT "FK_7abcb48f9239dda96ca591c048e" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        
        // VITALS - Blood Glucose
        await queryRunner.query(`ALTER TABLE "blood_glucose" DROP CONSTRAINT "FK_9505867bb285f81ae3bc52fccc6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9505867bb285f81ae3bc52fccc"`);
        await queryRunner.query(`ALTER TABLE "blood_glucose" ADD CONSTRAINT "FK_9505867bb285f81ae3bc52fccc6" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // VITALS - Anthropometry
        await queryRunner.query(`ALTER TABLE "anthropometry" DROP CONSTRAINT "FK_1057cdffa9c99fa29baa247e15b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1057cdffa9c99fa29baa247e15"`);
        await queryRunner.query(`ALTER TABLE "anthropometry" ADD CONSTRAINT "FK_1057cdffa9c99fa29baa247e15b" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        
        // VITALS
        await queryRunner.query(`ALTER TABLE "vitals" DROP CONSTRAINT "FK_f0a2dbdb3757326fc0e185d8133"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f0a2dbdb3757326fc0e185d813"`);
        await queryRunner.query(`ALTER TABLE "vitals" ADD CONSTRAINT "FK_f0a2dbdb3757326fc0e185d8133" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);


        // MY HEALTH - Pre Existing Condition
        await queryRunner.query(`ALTER TABLE "pre_existing_conditions" DROP CONSTRAINT "FK_0211fe5aa03a0639f1739c5d458"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0211fe5aa03a0639f1739c5d45"`);
        await queryRunner.query(`ALTER TABLE "pre_existing_conditions" ADD CONSTRAINT "FK_0211fe5aa03a0639f1739c5d458" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        
        // MY HEALTH - Physical Activity
        await queryRunner.query(`ALTER TABLE "physical_activity" DROP CONSTRAINT "FK_1d60c98a3886ae3d42f0ce99fb4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1d60c98a3886ae3d42f0ce99fb"`);
        await queryRunner.query(`ALTER TABLE "physical_activity" ADD CONSTRAINT "FK_1d60c98a3886ae3d42f0ce99fb4" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Past Surgery
        await queryRunner.query(`ALTER TABLE "past_surgery" DROP CONSTRAINT "FK_d08f7fcab9b81fc38fb0a63b1f7"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d08f7fcab9b81fc38fb0a63b1f"`);
        await queryRunner.query(`ALTER TABLE "past_surgery" ADD CONSTRAINT "FK_d08f7fcab9b81fc38fb0a63b1f7" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Past Encounter
        await queryRunner.query(`ALTER TABLE "past_encounters" DROP CONSTRAINT "FK_e80f83614efe26e1016b384c4ee"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e80f83614efe26e1016b384c4e"`);
        await queryRunner.query(`ALTER TABLE "past_encounters" ADD CONSTRAINT "FK_e80f83614efe26e1016b384c4ee" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Oncology History
        await queryRunner.query(`ALTER TABLE "oncology_histories" DROP CONSTRAINT "FK_d0c00b97364cb3bdf5a3f76f7e1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d0c00b97364cb3bdf5a3f76f7e"`);
        await queryRunner.query(`ALTER TABLE "oncology_histories" ADD CONSTRAINT "FK_d0c00b97364cb3bdf5a3f76f7e1" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Obstetric History
        await queryRunner.query(`ALTER TABLE "obstetric_histories" DROP CONSTRAINT "FK_fc18818f7dcc2643b836117607a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fc18818f7dcc2643b836117607"`);
        await queryRunner.query(`ALTER TABLE "obstetric_histories" ADD CONSTRAINT "FK_fc18818f7dcc2643b836117607a" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Next Of Kin
        await queryRunner.query(`ALTER TABLE "next_of_kin" DROP CONSTRAINT "FK_aae80bc271eb191298c26bda36d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_aae80bc271eb191298c26bda36"`);
        await queryRunner.query(`ALTER TABLE "next_of_kin" ADD CONSTRAINT "FK_aae80bc271eb191298c26bda36d" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Habit
        await queryRunner.query(`ALTER TABLE "habits" DROP CONSTRAINT "FK_37e754ef8e40b8418aef6174d8b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_37e754ef8e40b8418aef6174d8"`);
        await queryRunner.query(`ALTER TABLE "habits" ADD CONSTRAINT "FK_37e754ef8e40b8418aef6174d8b" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Gynecologic History
        await queryRunner.query(`ALTER TABLE "geynecologic_histories" DROP CONSTRAINT "FK_7cfe0923aac067ba2e3095bf27e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7cfe0923aac067ba2e3095bf27"`);
        await queryRunner.query(`ALTER TABLE "geynecologic_histories" ADD CONSTRAINT "FK_7cfe0923aac067ba2e3095bf27e" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Family History
        await queryRunner.query(`ALTER TABLE "family_histories" DROP CONSTRAINT "FK_ec713e64baa29cb1cc742ce4e5d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ec713e64baa29cb1cc742ce4e5"`);
        await queryRunner.query(`ALTER TABLE "family_histories" ADD CONSTRAINT "FK_ec713e64baa29cb1cc742ce4e5d" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Disability
        await queryRunner.query(`ALTER TABLE "disabilities" DROP CONSTRAINT "FK_aa45a88551278eaddb7a8262884"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_aa45a88551278eaddb7a826288"`);
        await queryRunner.query(`ALTER TABLE "disabilities" ADD CONSTRAINT "FK_aa45a88551278eaddb7a8262884" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // MY HEALTH - Dependencies
        await queryRunner.query(`ALTER TABLE "dependents" DROP CONSTRAINT "FK_8a01d09120b50f5497588861594"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8a01d09120b50f549758886159"`);
        await queryRunner.query(`ALTER TABLE "dependents" ADD CONSTRAINT "FK_8a01d09120b50f5497588861594" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);


        // PRE OPERATION CHECKLIST
        await queryRunner.query(`ALTER TABLE "pre_operation_checklist" DROP CONSTRAINT "FK_25a42fbfa39df8d2075e12a813f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_25a42fbfa39df8d2075e12a813"`);
        await queryRunner.query(`ALTER TABLE "pre_operation_checklist" ADD CONSTRAINT "FK_25a42fbfa39df8d2075e12a813f" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // PROCEDURE
        await queryRunner.query(`ALTER TABLE "surgeries" DROP CONSTRAINT "FK_987b5fb5345e88a3328cf642419"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_987b5fb5345e88a3328cf64241"`);
        await queryRunner.query(`ALTER TABLE "surgeries" ADD CONSTRAINT "FK_987b5fb5345e88a3328cf642419" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // CONSUMABLES
        await queryRunner.query(`ALTER TABLE "consumables" DROP CONSTRAINT "FK_1a9ba5bbdbf669c0fdba9e28f5f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1a9ba5bbdbf669c0fdba9e28f5"`);
        await queryRunner.query(`ALTER TABLE "consumables" ADD CONSTRAINT "FK_1a9ba5bbdbf669c0fdba9e28f5f" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // PRE AUTHORIZATION ****
        await queryRunner.query(`ALTER TABLE "pre_authorizations" DROP CONSTRAINT "FK_60fdacc39101912dba254b7ae17"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_60fdacc39101912dba254b7ae1"`);
        await queryRunner.query(`ALTER TABLE "pre_authorizations" ADD CONSTRAINT "FK_60fdacc39101912dba254b7ae17" FOREIGN KEY ("profile_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        
        // MEDICATION
        await queryRunner.query(`ALTER TABLE "medications" DROP CONSTRAINT "FK_d9eb5a949f0d1e384131a424565"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d9eb5a949f0d1e384131a42456"`);
        await queryRunner.query(`ALTER TABLE "medications" ADD CONSTRAINT "FK_d9eb5a949f0d1e384131a424565" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // INVESTIGATION
        await queryRunner.query(`ALTER TABLE "investigations" DROP CONSTRAINT "FK_d8057a9ae5d3771a25513ade706"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d8057a9ae5d3771a25513ade70"`);
        await queryRunner.query(`ALTER TABLE "investigations" ADD CONSTRAINT "FK_d8057a9ae5d3771a25513ade706" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // IMMUNIZATION
        await queryRunner.query(`ALTER TABLE "immunizations" DROP CONSTRAINT "FK_4ee1a43a5b1d7d1299f2d5d617f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4ee1a43a5b1d7d1299f2d5d617"`);
        await queryRunner.query(`ALTER TABLE "immunizations" ADD CONSTRAINT "FK_4ee1a43a5b1d7d1299f2d5d617f" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // HMO PROFILES ****
        await queryRunner.query(`ALTER TABLE "hmo_profiles" DROP CONSTRAINT "FK_208b0c02ca91e2cd1e246436e11"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_208b0c02ca91e2cd1e246436e1"`);
        await queryRunner.query(`ALTER TABLE "hmo_profiles" ADD CONSTRAINT "FK_208b0c02ca91e2cd1e246436e11" FOREIGN KEY ("profile_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // HMO CLAIMS ****
        await queryRunner.query(`ALTER TABLE "hmo_claims" DROP CONSTRAINT "FK_53a58dcc611b38b620d93342742"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_53a58dcc611b38b620d9334274"`);
        await queryRunner.query(`ALTER TABLE "hmo_claims" ADD CONSTRAINT "FK_53a58dcc611b38b620d93342742" FOREIGN KEY ("profile_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // CONSULTATION
        await queryRunner.query(`ALTER TABLE "consultations" DROP CONSTRAINT "FK_d94c63fd1745e496f509b2047af"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d94c63fd1745e496f509b2047a"`);
        await queryRunner.query(`ALTER TABLE "consultations" ADD CONSTRAINT "FK_d94c63fd1745e496f509b2047af" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // CARD
        await queryRunner.query(`ALTER TABLE "cards" DROP CONSTRAINT "FK_3958160cdc723507dcabef9787e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3958160cdc723507dcabef9787"`);
        await queryRunner.query(`ALTER TABLE "cards" ADD CONSTRAINT "FK_3958160cdc723507dcabef9787e" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // APPOINTMENT
        await queryRunner.query(`ALTER TABLE "organisation_appointments" DROP CONSTRAINT "FK_78480a42f74668cc89991f650d0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_78480a42f74668cc89991f650d"`);
        await queryRunner.query(`ALTER TABLE "organisation_appointments" ADD CONSTRAINT "FK_78480a42f74668cc89991f650d0" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // ANTENATAL
        await queryRunner.query(`ALTER TABLE "antenatals" DROP CONSTRAINT "FK_31a75238541428d4434091a46d0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_31a75238541428d4434091a46d"`);
        await queryRunner.query(`ALTER TABLE "antenatals" ADD CONSTRAINT "FK_31a75238541428d4434091a46d0" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // PARTOGRAPH
        await queryRunner.query(`ALTER TABLE "partograph" DROP CONSTRAINT "FK_a8703d7910ebaeb07b85856ab0c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a8703d7910ebaeb07b85856ab0"`);
        await queryRunner.query(`ALTER TABLE "partograph" ADD CONSTRAINT "FK_a8703d7910ebaeb07b85856ab0c" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

        // ALLERGIES
        await queryRunner.query(`ALTER TABLE "allergies" DROP CONSTRAINT "FK_58f7a9609aa76b6ea0b581b0280"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_58f7a9609aa76b6ea0b581b028"`);
        await queryRunner.query(`ALTER TABLE "allergies" ADD CONSTRAINT "FK_58f7a9609aa76b6ea0b581b0280" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    
        // ADMISSION
        await queryRunner.query(`ALTER TABLE "admissions" DROP CONSTRAINT "FK_48677d08e23d81ae6f5023528fa"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_48677d08e23d81ae6f5023528f"`);
        await queryRunner.query(`ALTER TABLE "admissions" ADD CONSTRAINT "FK_48677d08e23d81ae6f5023528fa" FOREIGN KEY ("profile") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }
}
