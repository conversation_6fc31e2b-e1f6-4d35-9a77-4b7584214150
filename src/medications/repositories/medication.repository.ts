/* eslint-disable max-lines */
import {
  MethodNotAllowedException,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import cloneDeep from 'lodash.clonedeep';
import difference from 'lodash.difference';
import moment from 'moment';
import { Repository, SelectQueryBuilder, In, EntityManager } from 'typeorm';
import { DispenseDetailsModel } from '../models/dispense-details.model';
import { DispenseRegistersModel } from '../models/dispense-register.model';
import { MedicationInfusionModel } from '../models/medication-infusion.model';
import { MedicationModel } from '../models/medication.model';
import { MedicationDetailsModel } from '../models/medication_details.model';
import { MedicationResponse } from '../responses/medication.response';
import { MedicationDetailsInput } from '../validators/medication-details.input';
import {
  DispenseDetailsInput,
  NewDispenseDetailsInput,
} from '../validators/medication-dispense-details.input';
import { MedicationInfusionInput } from '../validators/medication-infusion.input';
import { UpdateDispenseRegisterInput } from '../validators/medication-register.input';
import {
  DispenseStatus,
  DispenseType,
  MedFilterOptions,
} from '../validators/medication.filter.input';
import {
  MedicationInput,
  NewMedicationInput,
} from '../validators/medication.input';
import { BillService } from '@clinify/bills/services/bill.service';
import { OxygenTherapyModel } from '@clinify/medications/models/oxygen-therapy.model';
import { OxygenTherapyInput } from '@clinify/medications/validators/medication-oxygen-therapy.input';
import { BillableStatus } from '@clinify/shared/enums/bill';
import {
  BankType,
  MedicationOptionType,
} from '@clinify/shared/enums/medication';
import { UserType } from '@clinify/shared/enums/users';
import {
  makeDispenseRoster,
  makeRosterName,
} from '@clinify/shared/helper/medication/dispense-roster';
import { DateRangeInput } from '@clinify/shared/validators/date-range.input';
import { RecordCreator } from '@clinify/shared/validators/filter.input';
import {
  validateMedicationDispenseRemover,
  validateMedicationRecordRemover,
  validateMutator,
  validateNumberOfBillsOnRecords,
  validateRecordArchiver,
  validateRecordRemover,
  validateSubRecordCreation,
} from '@clinify/shared/validators/validate-record-mutation.validator';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { takePaginatedResponses } from '@clinify/utils/pagination/index';
import {
  cacheType,
  deleteCache,
  updateOverviewCacheOnUpdate,
} from '@clinify/utils/redis/overview-cache';

export interface IMedicationRepository extends Repository<MedicationModel> {
  this: Repository<MedicationModel>;

  buildProfileQuery(
    mutator: ProfileModel,
    input: {
      profileId?: string;
      hospitalId?: string;
      partnerId?: string;
      hmoId?: string;
    },
    options: Partial<MedFilterOptions>,
  ): SelectQueryBuilder<MedicationModel>;

  findByUserProfile(
    mutator: ProfileModel,
    profileId: string,
    medFilterOptions: Partial<MedFilterOptions>,
  ): Promise<MedicationResponse>;

  findByHospital(
    mutator: ProfileModel,
    hospitalId: string,
    medFilterOptions: Partial<MedFilterOptions>,
    hmoId?: string,
    partnerId?: string,
  ): Promise<MedicationResponse>;

  getPrescribedMedications(
    profileId: string,
    dateRange?: DateRangeInput,
  ): Promise<MedicationDetailsModel[]>;

  updateMedication(
    profile: ProfileModel,
    medication: MedicationInput,
    id: string,
  ): Promise<MedicationModel>;

  saveDispenseDetail(
    medicationId: string,
    dispenseDetails: DispenseDetailsInput,
    profile: ProfileModel,
  ): Promise<DispenseDetailsModel>;

  updateDispenseDetails(
    id: string,
    dispenseDetails: DispenseDetailsInput,
    profile: ProfileModel,
  ): Promise<[DispenseDetailsModel, boolean, DispenseDetailsModel]>;

  deleteMedication(
    profile: ProfileModel,
    medicationIds: string[],
  ): Promise<MedicationModel[]>;

  deleteDispenseDetail(
    profile: ProfileModel,
    id: string,
  ): Promise<DispenseDetailsModel>;

  archiveMedication(
    profile: ProfileModel,
    medicationIds: string[],
    archive: boolean,
    billService: BillService,
  ): Promise<MedicationModel[]>;

  getOneMedication(
    profile: ProfileModel,
    medicationId: string,
  ): Promise<MedicationModel>;

  getDispenseDetails(
    _profile: ProfileModel,
    medicationId: string,
  ): Promise<DispenseDetailsModel[]>;

  getSharedMedications(
    medicationId: string,
    options: Partial<MedFilterOptions>,
  ): Promise<MedicationResponse>;

  saveMedicationDetail(
    medicationId: string,
    detail: MedicationDetailsInput,
    profile: ProfileModel,
  ): Promise<MedicationDetailsModel>;

  updateMedicationDetail(
    id: string,
    detail: MedicationDetailsInput,
    profile: ProfileModel,
    ignoreDispense?: boolean,
  ): Promise<[MedicationDetailsModel, boolean]>;

  updateMedicationStatus(
    medicationDetailId: string,
    status: string,
    profile: ProfileModel,
  ): Promise<MedicationDetailsModel>;

  deleteMedicationDetail(
    profile: ProfileModel,
    id: string,
  ): Promise<MedicationDetailsModel>;

  concealMedicationAdditionalNote(
    profile: ProfileModel,
    medicationId: string,
    concealState: boolean,
  ): Promise<MedicationModel>;

  concealPrescriptionNote(
    profile: ProfileModel,
    medicationDetailsId: string,
    concealState: boolean,
  ): Promise<MedicationDetailsModel>;

  concealDispenseNote(
    profile: ProfileModel,
    dispenseDetailsId: string,
    concealState: boolean,
  ): Promise<DispenseDetailsModel>;

  updateDispenseRegister(
    profile: ProfileModel,
    id: string,
    input: UpdateDispenseRegisterInput,
  ): Promise<[DispenseRegistersModel, MedicationDetailsModel]>;

  updateOxygenTherapy(
    profile: ProfileModel,
    id: string,
    input: OxygenTherapyInput,
  ): Promise<[OxygenTherapyModel, MedicationDetailsModel]>;

  deleteMultipleMedicationDetails(
    profile: ProfileModel,
    ids: string[],
  ): Promise<MedicationDetailsModel[]>;

  updateMedicationInfusion(
    profile: ProfileModel,
    id: string,
    input: MedicationInfusionInput,
  ): Promise<[MedicationInfusionModel, MedicationDetailsModel]>;

  saveOncologyPrescribeMedications(
    mutator: ProfileModel,
    profile: ProfileModel,
    medication: NewMedicationInput,
  ): Promise<MedicationModel>;

  updateOncologyPrescribeMedication(
    mutator: ProfileModel,
    medication: MedicationDetailsInput[],
    medicationDetailId: string,
  ): Promise<MedicationDetailsModel[]>;

  deleteOncologyPrescribeMedicationDetails(
    manager: EntityManager,
    mutator: ProfileModel,
    id: string,
  ): Promise<[MedicationModel, DispenseDetailsModel[]]>;

  deleteOncologyPrescribeMedication(
    manager: EntityManager,
    mutator: ProfileModel,
    id: string,
  ): Promise<[MedicationModel, DispenseDetailsModel[]]>;
}

export const CustomMedicationMethods: Pick<
  IMedicationRepository,
  | 'buildProfileQuery'
  | 'findByUserProfile'
  | 'findByHospital'
  | 'getPrescribedMedications'
  | 'updateMedication'
  | 'saveDispenseDetail'
  | 'updateDispenseDetails'
  | 'deleteMedication'
  | 'deleteDispenseDetail'
  | 'archiveMedication'
  | 'getOneMedication'
  | 'getDispenseDetails'
  | 'getSharedMedications'
  | 'saveMedicationDetail'
  | 'updateMedicationDetail'
  | 'updateMedicationStatus'
  | 'deleteMedicationDetail'
  | 'concealMedicationAdditionalNote'
  | 'concealPrescriptionNote'
  | 'concealDispenseNote'
  | 'updateDispenseRegister'
  | 'updateOxygenTherapy'
  | 'updateMedicationInfusion'
  | 'deleteMultipleMedicationDetails'
  | 'saveOncologyPrescribeMedications'
  | 'updateOncologyPrescribeMedication'
  | 'deleteOncologyPrescribeMedicationDetails'
  | 'deleteOncologyPrescribeMedication'
> = {
  buildProfileQuery(
    this: IMedicationRepository,
    mutator: ProfileModel,
    input,
    options,
  ) {
    const {
      dateRange,
      keyword,
      creator,
      archive,
      dispenseType,
      status,
      providerInsight,
    } = {
      ...options,
    };
    let query = this.createQueryBuilder('medications')
      .leftJoinAndSelect('medications.profile', 'profile')
      .leftJoinAndSelect('profile.coverageDetails', 'coverageDetails')
      .leftJoinAndSelect('coverageDetails.hmoProfile', 'hmoProfile')
      .leftJoinAndSelect('hmoProfile.provider', 'provider')
      .leftJoinAndSelect('medications.hospital', 'hospital')
      .leftJoinAndSelect('medications.details', 'details')
      .leftJoinAndSelect('medications.dispenseDetails', 'dispenseDetails')
      .leftJoinAndSelect('dispenseDetails.billing', 'medicationBill')
      .leftJoinAndSelect('dispenseDetails.createdBy', 'dispenseCreatedBy');

    if (mutator.branchIds?.length)
      query = query.andWhere(
        `((hospital.facilityCreatedVisibility = false OR hospital.facilityCreatedVisibility IS NULL) 
        OR medications.hospital IN(:...branchIds))`,
        {
          branchIds: mutator.branchIds,
        },
      );

    if ((providerInsight && options.hospitalId) || input.hospitalId) {
      query = query.andWhere(
        '(hospital.id = :hospitalId OR dispenseCreatedBy.hospital = :hospitalId)',
        {
          hospitalId: options.hospitalId || input.hospitalId,
        },
      );
    }
    if (input.partnerId) {
      query = query.andWhere('(hospital.partnerId = :partnerId)', {
        partnerId: input.partnerId,
      });
    }
    if (input.hmoId) {
      query = query.andWhere('(hmoProfile.providerId = :hmoId)', {
        hmoId: input.hmoId,
      });
    }
    if (input.profileId) {
      query = query.andWhere('(medications.profile = :profileId)', {
        profileId: input.profileId,
      });
    }
    query.andWhere('medications.archived = :archived', { archived: !!archive });

    if (creator) {
      query = query
        .withDeleted()
        .leftJoinAndSelect('medications.createdBy', 'createdBy')
        .andWhere(
          creator === RecordCreator.SELF
            ? 'createdBy.type = :patient'
            : 'createdBy.type != :patient',
          { patient: UserType.Patient },
        );
    }

    if (status && status !== DispenseStatus.All) {
      query = query.andWhere(
        status === DispenseStatus.Prescribed
          ? 'dispenseDetails.medication IS NULL'
          : 'dispenseDetails.medication IS NOT NULL',
      );
    }

    if (dispenseType && dispenseType !== DispenseType.All) {
      query = query.andWhere(
        dispenseType === DispenseType.Internal
          ? '(dispenseCreatedBy.hospital = :id OR dispenseDetails.medication IS NULL)'
          : '(dispenseCreatedBy.hospital != :id AND dispenseDetails.medication IS NOT NULL)',
        {
          id: input.hospitalId,
        },
      );
    }

    if (keyword)
      query = query.andWhere(
        `(
          details.duration ILIKE :keyword OR
          details.medication_name ILIKE :keyword OR
          details.purpose ILIKE :keyword OR
          "medicationBill".bill::text ILIKE :keyword OR
          medications.prescribed_by ILIKE :keyword OR
          medications.hospital_name ILIKE :keyword OR
          profile.clinify_id ILIKE :keyword OR
          profile.full_name ILIKE :keyword OR
          details.administration_method ILIKE :keyword OR
          details.medication_consumables :: jsonb :: text ILIKE :keyword OR
          details.price_details::text ILIKE :keyword OR
          coverageDetails.coverageType ILIKE :keyword OR
          coverageDetails.name ILIKE :keyword OR
          coverageDetails.familyName ILIKE :keyword OR
          coverageDetails.companyName ILIKE :keyword OR
          coverageDetails.memberNumber ILIKE :keyword OR
          provider.name ILIKE :keyword OR
          hmoProfile.memberNumber ILIKE :keyword
        )`,
        {
          keyword: `%${keyword}%`,
        },
      );

    if (dateRange?.from) {
      query = query.andWhere(
        '(details.date_prescribed >= :from OR medicationBill.createdDate >= :from)',
        { from: dateRange.from },
      );
    }

    if (dateRange?.to) {
      query = query.andWhere(
        '(details.date_prescribed < :to OR medicationBill.createdDate >= :to)',
        { to: dateRange.to },
      );
    }
    return query;
  },

  async findByUserProfile(
    this: IMedicationRepository,
    mutator: ProfileModel,
    profileId: string,
    medFilterOptions: Partial<MedFilterOptions>,
  ): Promise<MedicationResponse> {
    const { skip = 0, take = 50 } = {
      ...medFilterOptions,
    };
    const query = this.buildProfileQuery(
      mutator,
      { profileId },
      medFilterOptions,
    )
      .orderBy('medications.createdDate', 'DESC')
      .skip(skip)
      .take(take);
    const medResponse = await query.getManyAndCount();
    return new MedicationResponse(...takePaginatedResponses(medResponse, take));
  },

  async findByHospital(
    this: IMedicationRepository,
    mutator: ProfileModel,
    hospitalId: string,
    medFilterOptions: Partial<MedFilterOptions>,
    hmoId?: string,
    partnerId?: string,
  ): Promise<MedicationResponse> {
    const {
      skip = 0,
      take = 50,
      ...builderQuery
    } = {
      ...medFilterOptions,
    };

    let query = this.buildProfileQuery(
      mutator,
      { hmoId, partnerId, hospitalId },
      builderQuery,
    );

    if (
      !hmoId &&
      !partnerId &&
      medFilterOptions.dispenseType === DispenseType.External &&
      medFilterOptions.agencyId
    ) {
      query = this.buildProfileQuery(
        mutator,
        { hmoId: medFilterOptions.agencyId, hospitalId: undefined },
        { ...builderQuery, dispenseType: DispenseType.All },
      );
    }

    query.orderBy('medications.createdDate', 'DESC').skip(skip).take(take);
    const medResponse = await query.getManyAndCount();
    return new MedicationResponse(...takePaginatedResponses(medResponse, take));
  },

  async getPrescribedMedications(
    this: IMedicationRepository,
    profileId: string,
    dateRange?: DateRangeInput,
  ): Promise<MedicationDetailsModel[]> {
    const query = await this.manager
      .createQueryBuilder(MedicationDetailsModel, 'medication_details')
      .leftJoinAndSelect('medication_details.medication', 'medication')
      .leftJoinAndSelect('medication.profile', 'profile')
      .where('profile.id = :profileId', {
        profileId,
      });

    if (dateRange?.from) {
      const from = moment(dateRange.from).startOf('day').toDate();
      query.andWhere('medication_details.date_prescribed >= :from', { from });
    }

    if (dateRange?.to) {
      const to = moment(dateRange.to).endOf('day').toDate();
      query.andWhere('medication_details.date_prescribed < :to', { to });
    }

    return query.getMany();
  },

  async updateMedication(
    this: IMedicationRepository,
    profile: ProfileModel,
    medication: MedicationInput,
    id: string,
  ): Promise<MedicationModel> {
    const medicationInfo = await this.createQueryBuilder('medications')
      .leftJoinAndSelect('medications.hospital', 'medicationHospital')
      .leftJoinAndSelect('medications.details', 'details')
      .leftJoinAndSelect('medications.profile', 'profile')
      .leftJoinAndSelect('medications.createdBy', 'createdBy')
      .where('medications.id = :id', { id })
      .andWhere('profile.clinifyId = :clinifyId', {
        clinifyId: medication?.clinifyId,
      })
      .getOne();

    if (!medicationInfo) throw new NotFoundException('Record Not Found');
    validateMutator(profile, medicationInfo);

    let newDispenseDetail = medication.dispenseDetails
      ? await this.manager.save(
          medication.dispenseDetails.map(
            (dispense) =>
              new DispenseDetailsModel({
                ...(dispense as any),
                createdBy: profile,
                creatorName: profile?.fullName,
                medication: medicationInfo,
              }),
          ),
        )
      : medicationInfo.dispenseDetails;

    newDispenseDetail = medication.dispenseDetails
      ? [...(medicationInfo.dispenseDetails || []), ...newDispenseDetail]
      : newDispenseDetail;

    medicationInfo.updatedBy = profile;
    await updateOverviewCacheOnUpdate(
      medicationInfo.profileId,
      cacheType.Medication,
      medicationInfo,
    );
    delete medication.details;
    return this.save({
      ...medicationInfo,
      lastModifierName: profile.fullName,
      ...medication,
      dispenseDetails: newDispenseDetail,
    });
  },

  async saveDispenseDetail(
    this: IMedicationRepository,
    medicationId: string,
    dispenseDetails: NewDispenseDetailsInput,
    profile: ProfileModel,
  ): Promise<DispenseDetailsModel> {
    const { medicationDetailId } = dispenseDetails;
    const medication = await this.findOneOrFail({
      where: { id: medicationId },
      join: {
        alias: 'medication',
        leftJoinAndSelect: {
          dispenseDetails: 'medication.dispenseDetails',
          createdBy: 'medication.createdBy',
          profile: 'medication.profile',
          hospital: 'medication.hospital',
          billing: 'dispenseDetails.billing',
          bill: 'billing.bill',
          billDetails: 'bill.details',
          senderHospital: 'bill.createdBy',
          receiverProfile: 'bill.receiverProfile',
        },
      },
    }).catch(() => {
      throw new NotFoundException('Record Not Found');
    });

    const medicationDetail = await this.manager.findOne(
      MedicationDetailsModel,
      {
        where: { id: medicationDetailId },
      },
    );
    const overrideFor = [
      UserType.Pharmacist,
      UserType.OrganizationAdmin,
      UserType.OrganizationDoctor,
      UserType.OrganizationNurse,
      UserType.OrganizationRecordOfficer,
      UserType.OrganizationBillingOfficer,
    ];
    validateSubRecordCreation(
      profile,
      medication,
      overrideFor.includes(profile.type as UserType),
    );
    const name = medicationDetail.medicationName
      ? [medicationDetail.medicationName]
      : [dispenseDetails.medicationName[0]];
    return this.manager.save(
      new DispenseDetailsModel({
        ...(dispenseDetails as any),
        bank: medicationDetail.bank || BankType.CLINIFY,
        medicationName: name,
        medication,
        medicationDetail,
        createdBy: profile,
        creatorName: profile.fullName,
        hospitalId: profile.hospitalId,
      }),
    );
  },

  async updateDispenseDetails(
    this: IMedicationRepository,
    id: string,
    dispenseDetails: DispenseDetailsInput,
    profile: ProfileModel,
  ): Promise<[DispenseDetailsModel, boolean, DispenseDetailsModel]> {
    const { medicationDetailId } = dispenseDetails;
    const dispenseDetailToUpdate = await this.manager
      .createQueryBuilder(DispenseDetailsModel, 'dispenseDetail')
      .leftJoinAndSelect('dispenseDetail.createdBy', 'createdBy')
      .leftJoinAndSelect('dispenseDetail.medication', 'medication')
      .leftJoinAndSelect('medication.profile', 'profile')
      .leftJoinAndSelect('dispenseDetail.billing', 'billing')
      .leftJoinAndSelect('billing.bill', 'bill')
      .leftJoinAndSelect('bill.details', 'billDetails')
      .leftJoinAndSelect('billDetails.createdBy', 'billDetailCreatedBy')
      .where('dispenseDetail.id = :id', { id })
      .getOne();

    if (!dispenseDetailToUpdate)
      throw new NotFoundException('Record Not Found');

    const medicationDetail = await this.manager.findOne(
      MedicationDetailsModel,
      {
        where: { id: medicationDetailId },
      },
    );

    validateMutator(profile, dispenseDetailToUpdate, [UserType.Pharmacist]);
    dispenseDetailToUpdate.updatedBy = profile;
    dispenseDetailToUpdate.lastModifierName = profile.fullName;
    const name = medicationDetail.medicationName
      ? [medicationDetail.medicationName]
      : [dispenseDetails.medicationName[0]];

    const updatedDispensedDetail = await this.manager.save(
      DispenseDetailsModel,
      {
        ...dispenseDetailToUpdate,
        ...dispenseDetails,
        medicationDetail,
        medicationName: name,
        ...(dispenseDetails.medicationDetailId !==
        dispenseDetailToUpdate.medicationDetailId
          ? { bank: medicationDetail.bank || BankType.CLINIFY }
          : {}),
      },
    );

    return [
      updatedDispensedDetail,
      dispenseDetails.medicationDetailId !==
        dispenseDetailToUpdate.medicationDetailId,
      dispenseDetailToUpdate,
    ];
  },

  async deleteMedication(
    this: IMedicationRepository,
    profile: ProfileModel,
    medicationIds: string[],
  ): Promise<MedicationModel[]> {
    const medications = await this.find({
      relations: ['createdBy'],
      join: {
        alias: 'medication',
        leftJoinAndSelect: {
          dispenseDetails: 'medication.dispenseDetails',
          billing: 'dispenseDetails.billing',
          bill: 'billing.bill',
          senderHospital: 'bill.senderHospital',
          receiverProfile: 'bill.receiverProfile',
          profile: 'medication.profile',
          details: 'medication.details',
        },
      },
      where: { id: In(medicationIds) },
    });
    const _validResources = validateMedicationRecordRemover(
      profile,
      medications,
    );

    const validResources = _validResources.filter(
      (_item) => !_item.verificationCode,
    );
    if (
      !validResources.length &&
      validResources.length < _validResources.length
    ) {
      throw new NotAcceptableException('Not Authorized To Delete This Record');
    }
    if (validResources.length) {
      await deleteCache(validResources[0], cacheType.Medication);
      await this.remove(
        cloneDeep(validResources).map((v) => ({
          ...v,
          deletedBy: {
            id: profile.id,
            fullName: profile.fullName,
            entityId: v.id,
          },
        })),
      );
    }
    return validResources;
  },

  async deleteDispenseDetail(
    this: IMedicationRepository,
    profile: ProfileModel,
    id: string,
  ): Promise<DispenseDetailsModel> {
    const dispenseDetail = await this.manager
      .findOneOrFail(DispenseDetailsModel, {
        where: { id },
        relations: ['createdBy', 'billing', 'medication', 'hmoClaim'],
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    const validResources = validateMedicationDispenseRemover(profile, [
      dispenseDetail,
    ]);
    if (!validResources.length)
      throw new NotAcceptableException('Not Authorized To Delete This Record');
    await this.manager.delete(DispenseDetailsModel, { id });

    const medication = dispenseDetail.medication;
    const dispenseIds = medication.dispenseIds;
    const remainingDispenseIds = dispenseIds?.filter((item) => item !== id);
    const billStatus = {
      ...(remainingDispenseIds?.length
        ? {}
        : { billStatus: BillableStatus.Pending }),
    };

    await this.manager
      .createQueryBuilder()
      .update(MedicationModel)
      .set({
        ...billStatus,
        updatedDate: () => 'updated_date',
      })
      .where('id = :id', { id: medication.id })
      .execute();

    return {
      ...dispenseDetail,
      medication: {
        ...dispenseDetail.medication,
        ...billStatus,
        dispenseIds: remainingDispenseIds,
      },
    };
  },

  async archiveMedication(
    this: IMedicationRepository,
    profile: ProfileModel,
    medicationIds: string[],
    archive: boolean,
    billService: BillService,
  ): Promise<MedicationModel[]> {
    const medications = await this.find({
      relations: [
        'createdBy',
        'hospital',
        'dispenseDetails',
        'dispenseDetails.billing',
        'dispenseDetails.billing.bill',
        'dispenseDetails.billing.bill.details',
        'dispenseDetails.billing.bill.createdBy',
        'dispenseDetails.billing.bill.receiverProfile',
      ],
      where: { id: In(medicationIds) },
    });
    let validResources = validateRecordArchiver(profile, medications);
    validResources = validateNumberOfBillsOnRecords(validResources, archive);
    if (validResources.length)
      await deleteCache(validResources[0], cacheType.Medication);

    const validIds = validResources.map((v) => v.id);
    if (!validIds.length) return [];
    await this.createQueryBuilder('medications')
      .update(MedicationModel)
      .set({ archived: archive, updatedDate: () => 'updated_date' })
      .whereInIds(validIds)
      .execute();

    const billIds = validResources
      ?.flatMap?.((resource) => resource?.dispenseDetails)
      ?.map((dispense) => dispense?.billing?.billId);
    if (billIds.length) await billService.archiveBillsR(billIds, archive);

    return validResources.map((v) => ({ ...v, archived: archive }));
  },

  async getOneMedication(
    this: IMedicationRepository,
    profile: ProfileModel,
    medicationId: string,
  ): Promise<MedicationModel> {
    const medication = this.createQueryBuilder('medications')
      .withDeleted()
      .leftJoinAndSelect('medications.hospital', 'hospital')
      .leftJoinAndSelect('medications.details', 'details')
      .leftJoinAndSelect('details.dispenseRegister', 'dispenseRegister')
      .leftJoinAndSelect('details.oxygenTherapy', 'oxygenTherapy')
      .leftJoinAndSelect('details.infusion', 'infusion')
      .where('medications.id = :id', { id: medicationId });

    return await medication.getOne();
  },

  async getDispenseDetails(
    this: IMedicationRepository,
    _profile: ProfileModel,
    medicationId: string,
  ): Promise<DispenseDetailsModel[]> {
    const dispenseDetails = this.manager
      .createQueryBuilder(DispenseDetailsModel, 'dispense_details')
      .leftJoinAndSelect(
        'dispense_details.medicationDetail',
        'medicationDetail',
      )
      .where('dispense_details.medication = :medicationId', { medicationId });
    return await dispenseDetails.getMany();
  },

  async getSharedMedications(
    this: IMedicationRepository,
    medicationId: string,
    options: Partial<MedFilterOptions>,
  ): Promise<MedicationResponse> {
    const { skip = 0, take = 50, creator } = { ...options };
    let query = this.createQueryBuilder('medications').innerJoin(
      'medications.sharedRecords',
      'sharedRecords',
      'sharedRecords.id = :id',
      {
        id: medicationId,
      },
    );
    if (creator) {
      query = query
        .innerJoinAndSelect('medications.createdBy', 'createdBy')
        .andWhere(
          `createdBy.type ${creator === RecordCreator.SELF ? '=' : '!='} :type`,
          { type: UserType.Patient },
        );
    }
    const response = await query.offset(skip).limit(take).getManyAndCount();
    return new MedicationResponse(...takePaginatedResponses(response, take));
  },

  async saveMedicationDetail(
    this: IMedicationRepository,
    medicationId: string,
    detail: MedicationDetailsInput,
    profile: ProfileModel,
  ): Promise<MedicationDetailsModel> {
    const medication = await this.findOneOrFail({
      where: { id: medicationId },
      relations: ['createdBy'],
    }).catch(() => {
      throw new NotFoundException('Record Not Found');
    });
    validateSubRecordCreation(profile, medication);
    // create roster
    let dispenseRegister: any = null;
    const onlyConsumable = detail.option === MedicationOptionType.C;
    if (!onlyConsumable) {
      const { frequency, duration, dosage, medicationName, dosageUnit } =
        detail;
      const { freq, roster } = makeDispenseRoster(frequency, duration);

      const newDetail = {
        medicationName: makeRosterName({
          medicationName,
          frequency,
          dosage,
          duration,
          dosageUnit,
        }),
        periodName: freq?.unit,
        periods: roster,
      };

      dispenseRegister = await this.manager.save(
        DispenseRegistersModel,
        new DispenseRegistersModel({
          creator: profile.fullName,
          details: newDetail,
        }),
      );
    }
    let oxygenTherapy = null;
    if (detail.medicationType === 'Oxygen Therapy') {
      oxygenTherapy = await this.manager.save(
        OxygenTherapyModel,
        new OxygenTherapyModel({
          creator: profile.fullName,
          details: [],
        }),
      );
    }
    let medicationInfusion = null;
    if (detail.medicationType === 'Parenteral Infusions') {
      medicationInfusion = await this.manager.save(
        MedicationInfusionModel,
        new MedicationInfusionModel({
          creator: profile.fullName,
          details: [
            {
              drugName: detail.medicationName,
              prescribedBy: profile.fullName,
              dosage: `${detail.dosage || ''}::${detail.dosageUnit || ''}`,
              route: detail.administrationMethod || '',
            },
          ],
        }),
      );
    }

    const newMedicationDetail = await this.manager.save(
      new MedicationDetailsModel({
        ...(detail as any),
        medication,
        createdBy: profile,
        creatorName: profile.fullName,
        dispenseRegister,
        oxygenTherapy,
        infusion: medicationInfusion,
        refProfile: medication.profileId,
        hospitalId: profile.hospitalId,
      }),
    );

    return newMedicationDetail;
  },

  async updateMedicationDetail(
    this: IMedicationRepository,
    id: string,
    detail: MedicationDetailsInput,
    profile: ProfileModel,
    ignoreDispense = false,
  ): Promise<[MedicationDetailsModel, boolean]> {
    const detailToUpdate = await this.manager
      .createQueryBuilder(MedicationDetailsModel, 'detail')
      .leftJoinAndSelect('detail.medication', 'medication')
      .leftJoinAndSelect('medication.hospital', 'hospital')
      .leftJoinAndSelect('medication.profile', 'profile')
      .leftJoinAndSelect('detail.dispenseRegister', 'dispenseRegister')
      .leftJoinAndSelect('detail.dispenseDetails', 'dispenseDetails')
      .leftJoinAndSelect('dispenseDetails.billing', 'billing')
      .leftJoinAndSelect('billing.bill', 'bill')
      .leftJoinAndSelect('bill.details', 'billDetails')
      .leftJoinAndSelect('bill.receiverProfile', 'receiverProfile')
      .leftJoinAndSelect('billDetails.createdBy', 'billDetailCreatedBy')
      .where('detail.id = :id', { id })
      .getOne();

    if (!detailToUpdate) throw new NotFoundException('Record Not Found');

    validateMutator(
      profile,
      {
        ...detailToUpdate,
        hospitalId: detailToUpdate?.medication.hospitalId,
      },
      undefined,
      true,
    );
    const register = detailToUpdate.dispenseRegister;
    if (
      register &&
      (detail?.frequency !== detailToUpdate.frequency ||
        detail?.duration !== detailToUpdate.duration ||
        detail.medicationName !== detailToUpdate.medicationName ||
        detail.dosage !== detailToUpdate.dosage ||
        detail.dosageUnit !== detailToUpdate.dosageUnit) &&
      detail.option !== MedicationOptionType.C
    ) {
      const regenerate = detail.frequency !== detailToUpdate.frequency;
      if (!ignoreDispense) {
        const { periods, freq, roster } = makeDispenseRoster(
          detail.frequency,
          detail.duration,
        );
        const newDetail = {
          ...register.details,
          medicationName: makeRosterName({
            medicationName: detail.medicationName,
            frequency: detail.frequency,
            dosage: detail.dosage,
            duration: detail.duration,
            dosageUnit: detail.dosageUnit,
          }),
          periodName: freq?.unit,
          periods: Array.from({ length: periods }, (_, idx) => {
            if (regenerate) return roster[idx];
            if (register.details.periods[idx])
              return register.details.periods[idx];
            return roster[idx];
          }),
        };
        register.details = newDetail;
        await this.manager.save(DispenseRegistersModel, {
          ...register,
          details: newDetail,
          updater: profile.fullName,
          lastModifierName: profile?.fullName,
        });
      }
    }

    detailToUpdate.updatedBy = profile;
    const updatedMedicationDetail = await this.manager.save(
      MedicationDetailsModel,
      {
        ...detailToUpdate,
        ...detail,
        lastModifierName: profile.fullName,
        ...(detail.option === MedicationOptionType.C
          ? {}
          : ignoreDispense
          ? {}
          : {
              dispenseRegister: register
                ? {
                    ...register,
                    updater: profile.fullName,
                  }
                : null,
            }),
      },
    );

    return [
      updatedMedicationDetail,
      detail.medicationName !== detailToUpdate.medicationName,
    ];
  },

  async updateMedicationStatus(
    this: IMedicationRepository,
    medicationDetailId: string,
    status: string,
    profile: ProfileModel,
  ): Promise<MedicationDetailsModel> {
    const medicationDetails = await this.manager
      .findOneOrFail(MedicationDetailsModel, {
        relations: ['medication', 'medication.profile'],
        where: {
          id: medicationDetailId,
        },
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    await this.createQueryBuilder()
      .update(MedicationDetailsModel)
      .set({
        updatedDate: () => 'updated_date',
        medicationStatus: status,
      })
      .where('id = :id', { id: medicationDetailId })
      .execute();

    return { ...medicationDetails, medicationStatus: status };
  },

  async deleteMedicationDetail(
    this: IMedicationRepository,
    profile: ProfileModel,
    id: string,
  ): Promise<MedicationDetailsModel> {
    const detail = await this.manager
      .findOneOrFail(MedicationDetailsModel, {
        where: {
          id,
        },
        relations: ['createdBy'],
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    const validResources = validateRecordRemover(profile, [detail]);
    if (!validResources.length)
      throw new NotAcceptableException('Not Authorized To Delete This Record');
    await this.manager.delete(MedicationDetailsModel, { id });
    return detail;
  },

  async concealMedicationAdditionalNote(
    profile: ProfileModel,
    medicationId: string,
    concealState: boolean,
  ): Promise<MedicationModel> {
    const medication = await this.findOneOrFail({
      where: { id: medicationId },
    }).catch(() => {
      throw new NotFoundException('Record Not Found');
    });

    await this.createQueryBuilder()
      .update(MedicationModel)
      .set({
        updatedDate: () => 'updated_date',
        concealAdditionalNote: concealState,
      })
      .where('id = :id', { id: medicationId })
      .execute();

    return { ...medication, concealAdditionalNote: concealState };
  },

  async concealPrescriptionNote(
    this: IMedicationRepository,
    profile: ProfileModel,
    medicationDetailsId: string,
    concealState: boolean,
  ): Promise<MedicationDetailsModel> {
    const medicationDetails = await this.manager
      .findOneOrFail(MedicationDetailsModel, {
        relations: ['medication', 'medication.profile'],
        where: {
          id: medicationDetailsId,
        },
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    await this.createQueryBuilder()
      .update(MedicationDetailsModel)
      .set({
        updatedDate: () => 'updated_date',
        concealPrescriptionNote: concealState,
      })
      .where('id = :id', { id: medicationDetailsId })
      .execute();

    return { ...medicationDetails, concealPrescriptionNote: concealState };
  },

  async concealDispenseNote(
    this: IMedicationRepository,
    profile: ProfileModel,
    dispenseDetailsId: string,
    concealState: boolean,
  ): Promise<DispenseDetailsModel> {
    const dispenseDetails = await this.manager
      .findOneOrFail(DispenseDetailsModel, {
        join: {
          alias: 'dispenseDetails',
          leftJoinAndSelect: {
            medication: 'dispenseDetails.medication',
            profile: 'medication.profile',
          },
        },
        where: { id: dispenseDetailsId },
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    await this.createQueryBuilder()
      .update(DispenseDetailsModel)
      .set({
        updatedDate: () => 'updated_date',
        concealDispenseNote: concealState,
      })
      .where('id = :id', { id: dispenseDetailsId })
      .execute();

    return { ...dispenseDetails, concealDispenseNote: concealState };
  },

  async updateDispenseRegister(
    this: IMedicationRepository,
    profile: ProfileModel,
    id: string,
    input: UpdateDispenseRegisterInput,
  ): Promise<[DispenseRegistersModel, MedicationDetailsModel]> {
    const record = await this.manager
      .findOneOrFail(DispenseRegistersModel, {
        join: {
          alias: 'dispenseRegister',
          leftJoinAndSelect: {
            detail: 'dispenseRegister.medicationDetail',
            medication: 'detail.medication',
            medHospital: 'medication.hospital',
            detailCreatedBy: 'detail.createdBy',
          },
        },
        where: { id },
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    const _input = input;
    record.details.periods.forEach((item, idx) => {
      // operate on periods
      const newValus = input.periods[idx];
      if (item.values !== newValus.values) {
        const valueArr = newValus.values.split('::');
        const existingValArr = item.values.split('::');
        // operate on period items
        existingValArr.forEach((value, index) => {
          const isSame = value === valueArr[index];
          if (!isSame) {
            const val = valueArr[index].split(',');
            if (val[1] === '0') {
              const audits = item.audits || [];
              const newAudit = audits.filter(
                (audit) => audit.checkId !== index + 1,
              );
              const diff = difference(audits, newAudit);
              if (diff.length && diff[0].profileId !== profile.id)
                throw new MethodNotAllowedException(
                  'Not Authorized To Modify This Record',
                );

              _input.periods[idx] = {
                ..._input.periods[idx],
                audits: newAudit,
              };
              return;
            }
            _input.periods[idx] = {
              ..._input.periods[idx],
              audits: [
                ...(item.audits || []),
                {
                  fullName: profile.fullName,
                  dateTime: new Date(),
                  checkId: index + 1,
                  profileId: profile.id,
                },
              ],
            };
          }
        });
      }
    });

    const register = await this.manager.save(DispenseRegistersModel, {
      ...record,
      details: { ...record.details, ..._input },
      updater: profile.fullName,
      lastModifierName: profile?.fullName,
    });
    return [
      register,
      { ...record.medicationDetail, dispenseRegister: register },
    ];
  },
  async updateOxygenTherapy(
    profile: ProfileModel,
    id: string,
    input: OxygenTherapyInput,
  ): Promise<[OxygenTherapyModel, MedicationDetailsModel]> {
    const record = await this.manager
      .findOneOrFail(OxygenTherapyModel, {
        join: {
          alias: 'oxygenTherapy',
          leftJoinAndSelect: {
            detail: 'oxygenTherapy.medicationDetail',
            medication: 'detail.medication',
            medHospital: 'medication.hospital',
            detailCreatedBy: 'detail.createdBy',
          },
        },
        where: { id },
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    const oxygenTherapy = await this.manager.save(OxygenTherapyModel, {
      ...record,
      ...input,
      updatedBy: profile,
      lastModifierName: profile.fullName,
    });

    return [
      oxygenTherapy,
      { ...oxygenTherapy.medicationDetail, oxygenTherapy },
    ];
  },
  async deleteMultipleMedicationDetails(
    profile: ProfileModel,
    ids: string[],
  ): Promise<MedicationDetailsModel[]> {
    const details = await this.manager.find(MedicationDetailsModel, {
      where: {
        id: In(ids),
      },
      relations: ['createdBy'],
    });
    const validResources = validateRecordRemover<MedicationDetailsModel>(
      profile,
      details,
    );
    await this.manager.delete(MedicationDetailsModel, {
      id: In(ids),
    });

    return validResources;
  },
  async updateMedicationInfusion(
    profile: ProfileModel,
    id: string,
    input: MedicationInfusionInput,
  ): Promise<[MedicationInfusionModel, MedicationDetailsModel]> {
    const record = await this.manager
      .findOneOrFail(MedicationInfusionModel, {
        join: {
          alias: 'infusion',
          leftJoinAndSelect: {
            detail: 'infusion.medicationDetail',
            medication: 'detail.medication',
            medHospital: 'medication.hospital',
            detailCreatedBy: 'detail.createdBy',
          },
        },
        where: { id },
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    const medicationInfusion = await this.manager.save(
      MedicationInfusionModel,
      {
        ...record,
        ...input,
        updatedBy: profile,
        lastModifierName: profile.fullName,
      },
    );

    return [
      medicationInfusion,
      { ...medicationInfusion.medicationDetail, infusion: medicationInfusion },
    ];
  },
  async saveOncologyPrescribeMedications(mutator, profile, input) {
    const details = input.details.map(
      (item) =>
        new MedicationDetailsModel({
          ...(item as any),
          createdBy: mutator,
          creatorName: mutator.fullName,
          refProfile: profile.id,
          hospitalId: mutator.hospitalId,
        }),
    );

    if (!details.length) return;

    const newMedication = new MedicationModel({
      ...input,
      details,
      createdBy: mutator,
      creatorName: mutator.fullName,
      profile,
      hospitalId: mutator.hospitalId,
      dispenseDetails: null,
    });
    const medication = await this.save(newMedication);

    return medication;
  },
  async updateOncologyPrescribeMedication(
    this: IMedicationRepository,
    mutator,
    inputs,
    medicationDetailsId,
  ) {
    const saved: MedicationDetailsModel[] = [];
    const medDetail = await this.manager
      .findOneOrFail(MedicationDetailsModel, {
        join: {
          alias: 'medicationDetail',
          leftJoinAndSelect: {
            medication: 'medicationDetail.medication',
            profile: 'medication.profile',
            createdBy: 'medicationDetail.createdBy',
          },
        },
        where: { id: medicationDetailsId },
      })
      .catch(() => {
        throw new NotFoundException('Medication Record Not Found');
      });

    for (const input of inputs) {
      if (input.id) {
        const [details] = await this.updateMedicationDetail(
          input.id,
          input,
          mutator,
          true,
        );
        saved.push(details);
      } else {
        const detail = new MedicationDetailsModel({
          ...(input as any),
          createdBy: mutator,
          creatorName: mutator.fullName,
          refProfile: medDetail.refProfile,
          hospitalId: mutator.hospitalId,
          medication: medDetail.medication,
        });
        const savedDetail = await this.manager.save(detail);
        saved.push(savedDetail);
      }
    }
    return saved;
  },
  async deleteOncologyPrescribeMedicationDetails(
    this: IMedicationRepository,
    entityManager: EntityManager,
    mutator,
    id,
  ): Promise<[MedicationModel, DispenseDetailsModel[]]> {
    const detail = await entityManager.findOne(MedicationDetailsModel, {
      where: { id },
      relations: ['createdBy', 'dispenseDetails'],
    });

    if (!detail?.medicationId) return [null, []];

    const medication = await entityManager.findOne(MedicationModel, {
      relations: ['createdBy'],
      join: {
        alias: 'medication',
        leftJoinAndSelect: {
          dispenseDetails: 'medication.dispenseDetails',
          billing: 'dispenseDetails.billing',
          bill: 'billing.bill',
          senderHospital: 'bill.senderHospital',
          receiverProfile: 'bill.receiverProfile',
          profile: 'medication.profile',
          details: 'medication.details',
        },
      },
      where: { id: detail.medicationId },
    });

    if (medication.details?.length === 1 && medication.details[0].id === id) {
      const validResources = validateMedicationRecordRemover(mutator, [
        medication,
      ]);

      if (!validResources.length) {
        throw new NotAcceptableException(
          'Not Authorized To Delete This Record',
        );
      }

      await deleteCache(validResources[0], cacheType.Medication);
      await entityManager.remove(
        MedicationModel,
        cloneDeep(validResources).map((v) => ({
          ...v,
          deletedBy: {
            id: mutator.id,
            fullName: mutator.fullName,
            entityId: v.id,
          },
        })),
      );
      return [validResources[0], medication?.dispenseDetails || []];
    }

    if (detail.dispenseDetails?.length) {
      return [medication, []];
    }

    await entityManager.delete(MedicationDetailsModel, { id });
    return [medication, []];
  },

  async deleteOncologyPrescribeMedication(
    this: IMedicationRepository,
    entityManager: EntityManager,
    mutator,
    id,
  ): Promise<[MedicationModel, DispenseDetailsModel[]]> {
    const detail = await entityManager.findOne(MedicationDetailsModel, {
      where: { id },
    });

    if (!detail?.medicationId) return [null, []];

    const medication = await entityManager.findOne(MedicationModel, {
      relations: ['createdBy'],
      join: {
        alias: 'medication',
        leftJoinAndSelect: {
          dispenseDetails: 'medication.dispenseDetails',
          billing: 'dispenseDetails.billing',
          bill: 'billing.bill',
          senderHospital: 'bill.senderHospital',
          receiverProfile: 'bill.receiverProfile',
          profile: 'medication.profile',
          details: 'medication.details',
        },
      },
      where: { id: detail.medicationId },
    });

    const validResources = validateMedicationRecordRemover(mutator, [
      medication,
    ]);

    if (!validResources.length) {
      throw new NotAcceptableException('Not Authorized To Delete This Record');
    }

    await deleteCache(validResources[0], cacheType.Medication);
    await entityManager.remove(
      MedicationModel,
      cloneDeep(validResources).map((v) => ({
        ...v,
        deletedBy: {
          id: mutator.id,
          fullName: mutator.fullName,
          entityId: v.id,
        },
      })),
    );
    return [validResources[0], medication?.dispenseDetails || []];
  },
};
